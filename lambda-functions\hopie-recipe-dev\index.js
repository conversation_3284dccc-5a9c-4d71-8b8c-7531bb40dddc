const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/recipes')) {
            return await getRecipes(event);
        } else if (httpMethod === 'POST' && path.includes('/recipes')) {
            return await createRecipe(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/recipe/')) {
            return await getRecipeById(event);
        } else if (httpMethod === 'PUT' && path.includes('/recipe/')) {
            return await updateRecipe(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/recipe/')) {
            return await deleteRecipe(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getRecipes(event) {
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'RECIPE'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            recipes: result.Items || []
        })
    };
}

async function createRecipe(body, event) {
    const recipeId = `recipe-${Date.now()}`;
    const { title, description, ingredients, instructions, cookingTime, difficulty } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA',
            GSI1PK: 'RECIPE',
            GSI1SK: recipeId,
            id: recipeId,
            title,
            description,
            ingredients,
            instructions,
            cookingTime,
            difficulty,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Recipe created successfully',
            recipe: params.Item
        })
    };
}

async function getRecipeById(event) {
    const recipeId = event.pathParameters?.id || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            recipe: result.Item || null
        })
    };
}

async function updateRecipe(body, event) {
    const recipeId = event.pathParameters?.id || 'recipe-123';
    const { title, description, ingredients, instructions, cookingTime, difficulty } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET title = :title, description = :description, ingredients = :ingredients, instructions = :instructions, cookingTime = :cookingTime, difficulty = :difficulty, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':title': title,
            ':description': description,
            ':ingredients': ingredients,
            ':instructions': instructions,
            ':cookingTime': cookingTime,
            ':difficulty': difficulty,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Recipe updated successfully',
            recipe: result.Attributes
        })
    };
}

async function deleteRecipe(event) {
    const recipeId = event.pathParameters?.id || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Recipe deleted successfully'
        })
    };
}
