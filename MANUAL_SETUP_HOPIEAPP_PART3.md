# 🚀 HopieApp - Configuración Manual Parte 3

## 4. <PERSON><PERSON><PERSON> (Continuación)

### 4.11 Función 10: ImageFunction
1. **Function name**: `hopie-image-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**: [Ver código en MANUAL_SETUP_HOPIEAPP_PART2.md - función StatsFunction]

### 4.12 Función 11: NotificationFunction
1. **Function name**: `hopie-notification-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/list')) {
            return await getNotifications(event);
        } else if (httpMethod === 'POST' && path.includes('/mark-read')) {
            return await markAsRead(requestBody, event);
        } else if (httpMethod === 'POST' && path.includes('/send')) {
            return await sendNotification(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/preferences')) {
            return await updateNotificationPreferences(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getNotifications(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'NOTIFICATION#'
        },
        ScanIndexForward: false,
        Limit: 50
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            notifications: result.Items || []
        })
    };
}

async function markAsRead(body, event) {
    const userId = 'user-123';
    const { notificationId } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: notificationId
        },
        UpdateExpression: 'SET #read = :read, readAt = :readAt',
        ExpressionAttributeNames: {
            '#read': 'read'
        },
        ExpressionAttributeValues: {
            ':read': true,
            ':readAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Notification marked as read',
            notification: result.Attributes
        })
    };
}

async function sendNotification(body, event) {
    const { userId, title, message, type, data } = body;
    const notificationId = `NOTIFICATION#${Date.now()}`;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: notificationId,
            title,
            message,
            type,
            data: data || {},
            read: false,
            createdAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Notification sent successfully',
            notificationId
        })
    };
}

async function updateNotificationPreferences(body, event) {
    const userId = 'user-123';
    const { pushEnabled, emailEnabled, smsEnabled, categories } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'NOTIFICATION_PREFERENCES'
        },
        UpdateExpression: 'SET pushEnabled = :push, emailEnabled = :email, smsEnabled = :sms, categories = :categories, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':push': pushEnabled,
            ':email': emailEnabled,
            ':sms': smsEnabled,
            ':categories': categories,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Notification preferences updated',
            preferences: result.Attributes
        })
    };
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`

### 4.13 Función 12: ChatFunction
1. **Function name**: `hopie-chat-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { requestContext, body } = event;
    const { routeKey, connectionId } = requestContext;
    
    try {
        if (routeKey === '$connect') {
            return await handleConnect(connectionId, event);
        } else if (routeKey === '$disconnect') {
            return await handleDisconnect(connectionId, event);
        } else if (routeKey === 'sendMessage') {
            return await handleSendMessage(connectionId, JSON.parse(body), event);
        } else if (routeKey === 'joinRoom') {
            return await handleJoinRoom(connectionId, JSON.parse(body), event);
        }
        
        return { statusCode: 200 };
    } catch (error) {
        console.error('Error:', error);
        return { statusCode: 500 };
    }
};

async function handleConnect(connectionId, event) {
    // Guardar conexión en DynamoDB
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: 'CONNECTION',
            SK: connectionId,
            userId: null, // Se establecerá cuando se autentique
            connectedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return { statusCode: 200 };
}

async function handleDisconnect(connectionId, event) {
    // Eliminar conexión de DynamoDB
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: 'CONNECTION',
            SK: connectionId
        }
    };
    
    await dynamodb.delete(params).promise();
    
    return { statusCode: 200 };
}

async function handleSendMessage(connectionId, body, event) {
    const { message, roomId, userId } = body;
    const messageId = `MESSAGE#${Date.now()}`;
    
    // Guardar mensaje en DynamoDB
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ROOM#${roomId}`,
            SK: messageId,
            userId,
            message,
            connectionId,
            timestamp: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    // Aquí implementarías el envío del mensaje a otros usuarios en la sala
    
    return { statusCode: 200 };
}

async function handleJoinRoom(connectionId, body, event) {
    const { roomId, userId } = body;
    
    // Actualizar conexión con información de sala
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: 'CONNECTION',
            SK: connectionId
        },
        UpdateExpression: 'SET userId = :userId, roomId = :roomId, joinedAt = :joinedAt',
        ExpressionAttributeValues: {
            ':userId': userId,
            ':roomId': roomId,
            ':joinedAt': new Date().toISOString()
        }
    };
    
    await dynamodb.update(params).promise();
    
    return { statusCode: 200 };
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`

### 4.14 Función 13: ImageProcessorFunction
1. **Function name**: `hopie-image-processor-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const s3 = new AWS.S3();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    try {
        for (const record of event.Records) {
            if (record.eventName.startsWith('ObjectCreated')) {
                await processNewImage(record);
            }
        }
        
        return { statusCode: 200 };
    } catch (error) {
        console.error('Error:', error);
        return { statusCode: 500 };
    }
};

async function processNewImage(record) {
    const bucket = record.s3.bucket.name;
    const key = record.s3.object.key;
    
    // Extraer información del path
    const pathParts = key.split('/');
    if (pathParts.length < 3 || pathParts[0] !== 'users') {
        return; // No es una imagen de usuario
    }
    
    const userId = pathParts[1];
    
    // Generar thumbnail (simulado)
    const thumbnailKey = key.replace('/images/', '/thumbnails/');
    
    // Actualizar metadata en DynamoDB
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :pk AND begins_with(GSI1SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'IMAGE#'
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    // Buscar el item correspondiente y actualizar con thumbnail
    for (const item of result.Items) {
        if (item.key === key) {
            const updateParams = {
                TableName: TABLE_NAME,
                Key: {
                    PK: item.PK,
                    SK: item.SK
                },
                UpdateExpression: 'SET thumbnailKey = :thumbnailKey, processed = :processed, processedAt = :processedAt',
                ExpressionAttributeValues: {
                    ':thumbnailKey': thumbnailKey,
                    ':processed': true,
                    ':processedAt': new Date().toISOString()
                }
            };
            
            await dynamodb.update(updateParams).promise();
            break;
        }
    }
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`

### 4.15 Función 14: SchedulerFunction
1. **Function name**: `hopie-scheduler-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    try {
        const source = event.source;
        
        if (source === 'aws.events') {
            // Evento programado de EventBridge
            const ruleName = event['detail-type'];
            
            if (ruleName === 'Daily Question Generation') {
                await generateDailyQuestion();
            } else if (ruleName === 'Streak Calculation') {
                await calculateStreaks();
            }
        }
        
        return { statusCode: 200 };
    } catch (error) {
        console.error('Error:', error);
        return { statusCode: 500 };
    }
};

async function generateDailyQuestion() {
    const today = new Date().toISOString().split('T')[0];
    
    // Verificar si ya existe pregunta para hoy
    const checkParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: 'DAILY_QUESTION',
            SK: today
        }
    };
    
    const existing = await dynamodb.get(checkParams).promise();
    
    if (existing.Item) {
        console.log('Daily question already exists for today');
        return;
    }
    
    // Generar nueva pregunta
    const questions = [
        "¿Cuál es tu recuerdo favorito de nuestra relación?",
        "¿Qué es lo que más admiras de tu pareja?",
        "¿Cuál sería tu cita ideal?",
        "¿Qué meta te gustaría lograr juntos este año?",
        "¿Cuál es tu tradición familiar favorita?",
        "¿Qué lugar del mundo te gustaría visitar juntos?",
        "¿Cuál es tu canción favorita de nuestra relación?",
        "¿Qué es lo que más te gusta hacer juntos?",
        "¿Cuál es tu comida favorita para compartir?",
        "¿Qué sueño tienes para nuestro futuro?"
    ];
    
    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: 'DAILY_QUESTION',
            SK: today,
            question: randomQuestion,
            category: 'relationship',
            createdAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    console.log('Daily question generated:', randomQuestion);
}

async function calculateStreaks() {
    // Obtener todos los usuarios activos
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :pk',
        ExpressionAttributeValues: {
            ':pk': 'ACTIVE_USERS'
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    for (const user of result.Items) {
        await updateUserStreak(user.userId);
    }
}

async function updateUserStreak(userId) {
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];
    
    // Verificar si el usuario respondió ayer
    const answerParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': `ANSWER#${yesterdayStr}`
        }
    };
    
    const answers = await dynamodb.query(answerParams).promise();
    
    // Obtener streak actual
    const streakParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'STREAK#CURRENT'
        }
    };
    
    const streakResult = await dynamodb.get(streakParams).promise();
    const currentStreak = streakResult.Item?.currentStreak || 0;
    
    let newStreak = 0;
    
    if (answers.Items.length > 0) {
        // Usuario respondió ayer, mantener o incrementar streak
        newStreak = currentStreak + 1;
    } else {
        // Usuario no respondió ayer, resetear streak
        newStreak = 0;
    }
    
    // Actualizar streak
    const updateParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'STREAK#CURRENT'
        },
        UpdateExpression: 'SET currentStreak = :streak, lastCalculated = :date',
        ExpressionAttributeValues: {
            ':streak': newStreak,
            ':date': today
        }
    };
    
    await dynamodb.update(updateParams).promise();
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`
