const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/questions')) {
            return await getQuestions(event);
        } else if (httpMethod === 'POST' && path.includes('/questions')) {
            return await createQuestion(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/question/')) {
            return await getQuestionById(event);
        } else if (httpMethod === 'PUT' && path.includes('/question/')) {
            return await updateQuestion(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/question/')) {
            return await deleteQuestion(event);
        } else if (httpMethod === 'POST' && path.includes('/question/answer')) {
            return await answerQuestion(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/daily-question')) {
            return await getDailyQuestion(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getQuestions(event) {
    const category = event.queryStringParameters?.category || 'all';

    let params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'QUESTION'
        }
    };

    if (category !== 'all') {
        params.FilterExpression = 'category = :category';
        params.ExpressionAttributeValues[':category'] = category;
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            questions: result.Items || []
        })
    };
}

async function createQuestion(body, event) {
    const questionId = `question-${Date.now()}`;
    const { text, category, type, options, correctAnswer } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA',
            GSI1PK: 'QUESTION',
            GSI1SK: questionId,
            id: questionId,
            text,
            category,
            type: type || 'multiple_choice',
            options: options || [],
            correctAnswer,
            difficulty: body.difficulty || 'medium',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Question created successfully',
            question: params.Item
        })
    };
}

async function getQuestionById(event) {
    const questionId = event.pathParameters?.id || 'question-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            question: result.Item || null
        })
    };
}

async function updateQuestion(body, event) {
    const questionId = event.pathParameters?.id || 'question-123';
    const { text, category, type, options, correctAnswer, difficulty } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #text = :text, category = :category, #type = :type, options = :options, correctAnswer = :correctAnswer, difficulty = :difficulty, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#text': 'text',
            '#type': 'type'
        },
        ExpressionAttributeValues: {
            ':text': text,
            ':category': category,
            ':type': type,
            ':options': options,
            ':correctAnswer': correctAnswer,
            ':difficulty': difficulty,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Question updated successfully',
            question: result.Attributes
        })
    };
}

async function deleteQuestion(event) {
    const questionId = event.pathParameters?.id || 'question-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Question deleted successfully'
        })
    };
}

async function answerQuestion(body, event) {
    const userId = 'user-123';
    const { questionId, answer, timeSpent } = body;
    const answerId = `answer-${Date.now()}`;

    // Get the question to check correct answer
    const questionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        }
    };

    const questionResult = await dynamodb.get(questionParams).promise();
    const isCorrect = questionResult.Item ? questionResult.Item.correctAnswer === answer : false;

    // Save the answer
    const answerParams = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `ANSWER#${answerId}`,
            id: answerId,
            userId,
            questionId,
            answer,
            isCorrect,
            timeSpent,
            answeredAt: new Date().toISOString()
        }
    };

    await dynamodb.put(answerParams).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Answer recorded successfully',
            isCorrect,
            correctAnswer: questionResult.Item?.correctAnswer,
            answer: answerParams.Item
        })
    };
}

async function getDailyQuestion(event) {
    const today = new Date().toISOString().split('T')[0];

    // Get all questions and select one based on today's date
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'QUESTION'
        }
    };

    const result = await dynamodb.query(params).promise();
    const questions = result.Items || [];

    if (questions.length === 0) {
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'No questions available' })
        };
    }

    // Use date as seed for consistent daily question
    const dateHash = today.split('-').reduce((a, b) => parseInt(a) + parseInt(b), 0);
    const questionIndex = dateHash % questions.length;
    const dailyQuestion = questions[questionIndex];

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            date: today,
            question: dailyQuestion
        })
    };
}
