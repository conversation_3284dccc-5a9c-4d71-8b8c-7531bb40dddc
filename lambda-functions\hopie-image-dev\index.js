const AWS = require('aws-sdk');
const s3 = new AWS.S3();
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;
const BUCKET_NAME = process.env.BUCKET_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/upload-url')) {
            return await getUploadUrl(requestBody, event);
        } else if (httpMethod === 'POST' && path.includes('/images')) {
            return await saveImageMetadata(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/images')) {
            return await getImages(event);
        } else if (httpMethod === 'DELETE' && path.includes('/image/')) {
            return await deleteImage(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUploadUrl(body, event) {
    const { fileName, fileType } = body;
    const key = `images/${Date.now()}-${fileName}`;

    const params = {
        Bucket: BUCKET_NAME,
        Key: key,
        ContentType: fileType,
        Expires: 300
    };

    const uploadUrl = s3.getSignedUrl('putObject', params);

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            uploadUrl,
            key,
            downloadUrl: `https://${BUCKET_NAME}.s3.amazonaws.com/${key}`
        })
    };
}

async function saveImageMetadata(body, event) {
    const imageId = `image-${Date.now()}`;
    const { key, fileName, fileType, entityId, entityType } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `IMAGE#${imageId}`,
            SK: 'METADATA',
            GSI1PK: `${entityType}#${entityId}`,
            GSI1SK: `IMAGE#${imageId}`,
            id: imageId,
            key,
            fileName,
            fileType,
            entityId,
            entityType,
            url: `https://${BUCKET_NAME}.s3.amazonaws.com/${key}`,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Image metadata saved successfully',
            image: params.Item
        })
    };
}

async function getImages(event) {
    const entityId = event.queryStringParameters?.entityId;
    const entityType = event.queryStringParameters?.entityType;

    if (!entityId || !entityType) {
        return {
            statusCode: 400,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'entityId and entityType are required' })
        };
    }

    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk AND begins_with(GSI1SK, :gsi1sk)',
        ExpressionAttributeValues: {
            ':gsi1pk': `${entityType}#${entityId}`,
            ':gsi1sk': 'IMAGE#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            images: result.Items || []
        })
    };
}

async function deleteImage(event) {
    const imageId = event.pathParameters?.id || 'image-123';

    // Get image metadata first
    const getParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `IMAGE#${imageId}`,
            SK: 'METADATA'
        }
    };

    const imageResult = await dynamodb.get(getParams).promise();

    if (imageResult.Item) {
        // Delete from S3
        const deleteS3Params = {
            Bucket: BUCKET_NAME,
            Key: imageResult.Item.key
        };

        await s3.deleteObject(deleteS3Params).promise();

        // Delete from DynamoDB
        await dynamodb.delete(getParams).promise();
    }

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Image deleted successfully'
        })
    };
}
