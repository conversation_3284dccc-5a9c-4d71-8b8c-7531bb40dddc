const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('WebSocket Disconnect Event:', JSON.stringify(event, null, 2));

    const { requestContext } = event;
    const { connectionId } = requestContext;

    try {
        // Remove connection from main connections table
        const connectionParams = {
            TableName: TABLE_NAME,
            Key: {
                PK: `CONNECTION#${connectionId}`,
                SK: 'METADATA'
            }
        };

        await dynamodb.delete(connectionParams).promise();

        // Remove connection from all rooms
        const roomsParams = {
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk AND GSI1SK = :gsi1sk',
            ExpressionAttributeValues: {
                ':gsi1pk': 'CONNECTION',
                ':gsi1sk': connectionId
            }
        };

        const roomsResult = await dynamodb.query(roomsParams).promise();

        // Remove from each room
        const deletePromises = (roomsResult.Items || []).map(item => {
            return dynamodb.delete({
                TableName: TABLE_NAME,
                Key: {
                    PK: item.PK,
                    SK: item.SK
                }
            }).promise();
        });

        await Promise.all(deletePromises);

        console.log(`Connection ${connectionId} disconnected and cleaned up`);

        return {
            statusCode: 200,
            body: 'Disconnected'
        };
    } catch (error) {
        console.error('Error in disconnect:', error);
        return {
            statusCode: 500,
            body: 'Failed to disconnect'
        };
    }
};
