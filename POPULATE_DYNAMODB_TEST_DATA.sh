#!/bin/bash

# 🚀 Script para poblar DynamoDB con datos de prueba para HopieApp
# Inserta datos de prueba para todas las entidades

TABLE_NAME="hopie-main-table-dev"
REGION="us-east-2"

echo "📊 Poblando tabla DynamoDB con datos de prueba..."
echo "📋 Tabla: $TABLE_NAME"
echo "🌍 Región: $REGION"
echo ""

# Función para insertar un item individual
insert_item() {
    local item_json="$1"
    local description="$2"
    
    echo "🔄 Insertando: $description"
    
    aws dynamodb put-item \
        --table-name $TABLE_NAME \
        --item "$item_json" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $description insertado exitosamente"
    else
        echo "   ❌ Error insertando $description"
    fi
}

echo "👤 Insertando usuarios de prueba..."

# Usuario 1: <PERSON>
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "PROFILE"},
    "id": {"S": "user-123"},
    "name": {"S": "<PERSON>"},
    "email": {"S": "<EMAIL>"},
    "phone": {"S": "+1234567890"},
    "birthDate": {"S": "1990-05-15"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Usuario Juan Pérez"

# Usuario 2: María García
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "PROFILE"},
    "id": {"S": "user-456"},
    "name": {"S": "María García"},
    "email": {"S": "<EMAIL>"},
    "phone": {"S": "+1234567891"},
    "birthDate": {"S": "1992-08-22"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Usuario María García"

# Usuario 3: Carlos López
insert_item '{
    "PK": {"S": "USER#user-789"},
    "SK": {"S": "PROFILE"},
    "id": {"S": "user-789"},
    "name": {"S": "Carlos López"},
    "email": {"S": "<EMAIL>"},
    "phone": {"S": "+1234567892"},
    "birthDate": {"S": "1988-12-10"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Usuario Carlos López"

echo ""
echo "🍳 Insertando recetas de prueba..."

# Receta 1: Pasta Carbonara
insert_item '{
    "PK": {"S": "RECIPE#recipe-001"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "RECIPE"},
    "GSI1SK": {"S": "recipe-001"},
    "id": {"S": "recipe-001"},
    "title": {"S": "Pasta Carbonara"},
    "description": {"S": "Deliciosa pasta italiana con huevo y panceta"},
    "ingredients": {"L": [
        {"S": "400g pasta"},
        {"S": "200g panceta"},
        {"S": "4 huevos"},
        {"S": "100g queso parmesano"}
    ]},
    "instructions": {"L": [
        {"S": "Cocinar la pasta al dente"},
        {"S": "Freír la panceta hasta dorar"},
        {"S": "Mezclar huevos con queso"},
        {"S": "Combinar todo fuera del fuego"}
    ]},
    "cookingTime": {"N": "20"},
    "difficulty": {"S": "medium"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Receta Pasta Carbonara"

# Receta 2: Ensalada César
insert_item '{
    "PK": {"S": "RECIPE#recipe-002"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "RECIPE"},
    "GSI1SK": {"S": "recipe-002"},
    "id": {"S": "recipe-002"},
    "title": {"S": "Ensalada César"},
    "description": {"S": "Fresca ensalada con pollo y aderezo césar"},
    "ingredients": {"L": [
        {"S": "1 lechuga romana"},
        {"S": "200g pechuga de pollo"},
        {"S": "50g queso parmesano"},
        {"S": "Crutones"},
        {"S": "Aderezo césar"}
    ]},
    "instructions": {"L": [
        {"S": "Lavar y cortar la lechuga"},
        {"S": "Cocinar el pollo a la plancha"},
        {"S": "Preparar crutones"},
        {"S": "Mezclar con aderezo"}
    ]},
    "cookingTime": {"N": "15"},
    "difficulty": {"S": "easy"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Receta Ensalada César"

# Receta 3: Paella Valenciana
insert_item '{
    "PK": {"S": "RECIPE#recipe-003"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "RECIPE"},
    "GSI1SK": {"S": "recipe-003"},
    "id": {"S": "recipe-003"},
    "title": {"S": "Paella Valenciana"},
    "description": {"S": "Tradicional paella española con mariscos"},
    "ingredients": {"L": [
        {"S": "400g arroz bomba"},
        {"S": "500g mariscos mixtos"},
        {"S": "200g judías verdes"},
        {"S": "1 pimiento rojo"},
        {"S": "Azafrán"}
    ]},
    "instructions": {"L": [
        {"S": "Sofreír verduras"},
        {"S": "Añadir arroz y caldo"},
        {"S": "Incorporar mariscos"},
        {"S": "Cocinar 18 minutos"}
    ]},
    "cookingTime": {"N": "45"},
    "difficulty": {"S": "hard"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Receta Paella Valenciana"

echo ""
echo "🥕 Insertando ingredientes de prueba..."

# Ingrediente 1: Tomate
insert_item '{
    "PK": {"S": "INGREDIENT#ingredient-001"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "INGREDIENT"},
    "GSI1SK": {"S": "ingredient-001"},
    "id": {"S": "ingredient-001"},
    "name": {"S": "Tomate"},
    "category": {"S": "Verduras"},
    "unit": {"S": "kg"},
    "nutritionalInfo": {"M": {
        "calories": {"N": "18"},
        "protein": {"N": "0.9"},
        "carbs": {"N": "3.9"},
        "fat": {"N": "0.2"}
    }},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Ingrediente Tomate"

# Ingrediente 2: Pollo
insert_item '{
    "PK": {"S": "INGREDIENT#ingredient-002"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "INGREDIENT"},
    "GSI1SK": {"S": "ingredient-002"},
    "id": {"S": "ingredient-002"},
    "name": {"S": "Pollo"},
    "category": {"S": "Carnes"},
    "unit": {"S": "kg"},
    "nutritionalInfo": {"M": {
        "calories": {"N": "165"},
        "protein": {"N": "31"},
        "carbs": {"N": "0"},
        "fat": {"N": "3.6"}
    }},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Ingrediente Pollo"

# Ingrediente 3: Arroz
insert_item '{
    "PK": {"S": "INGREDIENT#ingredient-003"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "INGREDIENT"},
    "GSI1SK": {"S": "ingredient-003"},
    "id": {"S": "ingredient-003"},
    "name": {"S": "Arroz"},
    "category": {"S": "Cereales"},
    "unit": {"S": "kg"},
    "nutritionalInfo": {"M": {
        "calories": {"N": "130"},
        "protein": {"N": "2.7"},
        "carbs": {"N": "28"},
        "fat": {"N": "0.3"}
    }},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Ingrediente Arroz"

echo ""
echo "🛒 Insertando listas de compras de prueba..."

# Lista de compras 1
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SHOPPING_LIST#list-001"},
    "id": {"S": "list-001"},
    "name": {"S": "Compras de la semana"},
    "items": {"L": [
        {"M": {"name": {"S": "Leche"}, "quantity": {"N": "2"}, "unit": {"S": "litros"}, "completed": {"BOOL": false}}},
        {"M": {"name": {"S": "Pan"}, "quantity": {"N": "1"}, "unit": {"S": "unidad"}, "completed": {"BOOL": false}}},
        {"M": {"name": {"S": "Huevos"}, "quantity": {"N": "12"}, "unit": {"S": "unidades"}, "completed": {"BOOL": true}}}
    ]},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Lista de compras semanal"

# Lista de compras 2
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SHOPPING_LIST#list-002"},
    "id": {"S": "list-002"},
    "name": {"S": "Ingredientes para cena"},
    "items": {"L": [
        {"M": {"name": {"S": "Pasta"}, "quantity": {"N": "500"}, "unit": {"S": "gramos"}, "completed": {"BOOL": false}}},
        {"M": {"name": {"S": "Salsa de tomate"}, "quantity": {"N": "1"}, "unit": {"S": "lata"}, "completed": {"BOOL": false}}},
        {"M": {"name": {"S": "Queso"}, "quantity": {"N": "200"}, "unit": {"S": "gramos"}, "completed": {"BOOL": false}}}
    ]},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Lista ingredientes cena"

# Lista de compras 3
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SHOPPING_LIST#list-003"},
    "id": {"S": "list-003"},
    "name": {"S": "Frutas y verduras"},
    "items": {"L": [
        {"M": {"name": {"S": "Manzanas"}, "quantity": {"N": "6"}, "unit": {"S": "unidades"}, "completed": {"BOOL": true}}},
        {"M": {"name": {"S": "Lechuga"}, "quantity": {"N": "1"}, "unit": {"S": "unidad"}, "completed": {"BOOL": false}}},
        {"M": {"name": {"S": "Zanahorias"}, "quantity": {"N": "1"}, "unit": {"S": "kg"}, "completed": {"BOOL": false}}}
    ]},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Lista frutas y verduras"

echo ""
echo "🌳 Insertando árboles de prueba..."

# Árbol 1: Roble de la Esperanza
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "TREE#tree-001"},
    "GSI1PK": {"S": "TREE"},
    "GSI1SK": {"S": "tree-001"},
    "id": {"S": "tree-001"},
    "userId": {"S": "user-123"},
    "name": {"S": "Roble de la Esperanza"},
    "type": {"S": "oak"},
    "location": {"S": "Jardín principal"},
    "plantedDate": {"S": "2024-01-01T00:00:00.000Z"},
    "growthLevel": {"N": "3"},
    "waterLevel": {"N": "85"},
    "health": {"N": "95"},
    "lastWatered": {"S": "2024-01-10T08:00:00.000Z"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-10T08:00:00.000Z"}
}' "Árbol Roble de la Esperanza"

# Árbol 2: Cerezo del Amor
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "TREE#tree-002"},
    "GSI1PK": {"S": "TREE"},
    "GSI1SK": {"S": "tree-002"},
    "id": {"S": "tree-002"},
    "userId": {"S": "user-123"},
    "name": {"S": "Cerezo del Amor"},
    "type": {"S": "cherry"},
    "location": {"S": "Patio trasero"},
    "plantedDate": {"S": "2024-01-05T00:00:00.000Z"},
    "growthLevel": {"N": "2"},
    "waterLevel": {"N": "70"},
    "health": {"N": "88"},
    "lastWatered": {"S": "2024-01-09T18:00:00.000Z"},
    "createdAt": {"S": "2024-01-05T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-09T18:00:00.000Z"}
}' "Árbol Cerezo del Amor"

# Árbol 3: Pino de la Sabiduría
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "TREE#tree-003"},
    "GSI1PK": {"S": "TREE"},
    "GSI1SK": {"S": "tree-003"},
    "id": {"S": "tree-003"},
    "userId": {"S": "user-123"},
    "name": {"S": "Pino de la Sabiduría"},
    "type": {"S": "pine"},
    "location": {"S": "Entrada principal"},
    "plantedDate": {"S": "2024-01-08T00:00:00.000Z"},
    "growthLevel": {"N": "1"},
    "waterLevel": {"N": "60"},
    "health": {"N": "92"},
    "lastWatered": {"S": "2024-01-11T07:30:00.000Z"},
    "createdAt": {"S": "2024-01-08T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-11T07:30:00.000Z"}
}' "Árbol Pino de la Sabiduría"

echo ""
echo "❤️ Insertando relaciones de pareja de prueba..."

# Relación de pareja 1
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "COUPLE#couple-001"},
    "GSI1PK": {"S": "COUPLE"},
    "GSI1SK": {"S": "couple-001"},
    "id": {"S": "couple-001"},
    "userId": {"S": "user-123"},
    "partnerEmail": {"S": "<EMAIL>"},
    "partnerUserId": {"S": "user-456"},
    "relationshipType": {"S": "dating"},
    "startDate": {"S": "2023-12-01"},
    "status": {"S": "active"},
    "createdAt": {"S": "2023-12-01T00:00:00.000Z"},
    "updatedAt": {"S": "2023-12-01T00:00:00.000Z"}
}' "Relación Juan y María"

# Relación de pareja 2
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "COUPLE#couple-002"},
    "GSI1PK": {"S": "COUPLE"},
    "GSI1SK": {"S": "couple-002"},
    "id": {"S": "couple-002"},
    "userId": {"S": "user-456"},
    "partnerEmail": {"S": "<EMAIL>"},
    "partnerUserId": {"S": "user-123"},
    "relationshipType": {"S": "dating"},
    "startDate": {"S": "2023-12-01"},
    "status": {"S": "active"},
    "createdAt": {"S": "2023-12-01T00:00:00.000Z"},
    "updatedAt": {"S": "2023-12-01T00:00:00.000Z"}
}' "Relación María y Juan"

# Relación pendiente
insert_item '{
    "PK": {"S": "USER#user-789"},
    "SK": {"S": "COUPLE#couple-003"},
    "GSI1PK": {"S": "COUPLE"},
    "GSI1SK": {"S": "couple-003"},
    "id": {"S": "couple-003"},
    "userId": {"S": "user-789"},
    "partnerEmail": {"S": "<EMAIL>"},
    "relationshipType": {"S": "friendship"},
    "startDate": {"S": "2024-01-10"},
    "status": {"S": "pending"},
    "createdAt": {"S": "2024-01-10T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-10T00:00:00.000Z"}
}' "Relación pendiente Carlos"

echo ""
echo "❓ Insertando preguntas de prueba..."

# Pregunta 1: Abierta sobre comida
insert_item '{
    "PK": {"S": "QUESTION#question-001"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "QUESTION"},
    "GSI1SK": {"S": "question-001"},
    "id": {"S": "question-001"},
    "text": {"S": "¿Cuál es tu comida favorita y por qué?"},
    "category": {"S": "food"},
    "type": {"S": "open"},
    "difficulty": {"S": "easy"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Pregunta sobre comida favorita"

# Pregunta 2: Múltiple sobre cocina
insert_item '{
    "PK": {"S": "QUESTION#question-002"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "QUESTION"},
    "GSI1SK": {"S": "question-002"},
    "id": {"S": "question-002"},
    "text": {"S": "¿Qué ingrediente es esencial en la pasta carbonara?"},
    "category": {"S": "cooking"},
    "type": {"S": "multiple_choice"},
    "options": {"L": [
        {"S": "Crema"},
        {"S": "Huevo"},
        {"S": "Mantequilla"},
        {"S": "Aceite"}
    ]},
    "correctAnswer": {"S": "Huevo"},
    "difficulty": {"S": "medium"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Pregunta sobre carbonara"

# Pregunta 3: Múltiple sobre paella
insert_item '{
    "PK": {"S": "QUESTION#question-003"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "QUESTION"},
    "GSI1SK": {"S": "question-003"},
    "id": {"S": "question-003"},
    "text": {"S": "¿Cuántos minutos se debe cocinar la paella tradicionalmente?"},
    "category": {"S": "cooking"},
    "type": {"S": "multiple_choice"},
    "options": {"L": [
        {"S": "15 minutos"},
        {"S": "18 minutos"},
        {"S": "25 minutos"},
        {"S": "30 minutos"}
    ]},
    "correctAnswer": {"S": "18 minutos"},
    "difficulty": {"S": "hard"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Pregunta sobre paella"

echo ""
echo "🎯 Insertando planes de vida de prueba..."

# Plan de vida 1: Habilidades culinarias
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "LIFEPLAN#plan-001"},
    "GSI1PK": {"S": "LIFEPLAN"},
    "GSI1SK": {"S": "plan-001"},
    "id": {"S": "plan-001"},
    "userId": {"S": "user-123"},
    "title": {"S": "Mejorar habilidades culinarias"},
    "description": {"S": "Aprender a cocinar 20 recetas nuevas este año"},
    "category": {"S": "cooking"},
    "targetDate": {"S": "2024-12-31"},
    "goals": {"L": [
        {"M": {
            "id": {"S": "goal-001"},
            "title": {"S": "Aprender pasta italiana"},
            "description": {"S": "Dominar 5 recetas de pasta"},
            "completed": {"BOOL": true},
            "priority": {"S": "high"}
        }},
        {"M": {
            "id": {"S": "goal-002"},
            "title": {"S": "Dominar técnicas de paella"},
            "description": {"S": "Cocinar paella perfecta"},
            "completed": {"BOOL": false},
            "priority": {"S": "medium"}
        }}
    ]},
    "progress": {"N": "25"},
    "status": {"S": "active"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-10T00:00:00.000Z"}
}' "Plan habilidades culinarias"

# Plan de vida 2: Vida saludable
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "LIFEPLAN#plan-002"},
    "GSI1PK": {"S": "LIFEPLAN"},
    "GSI1SK": {"S": "plan-002"},
    "id": {"S": "plan-002"},
    "userId": {"S": "user-123"},
    "title": {"S": "Vida saludable"},
    "description": {"S": "Adoptar hábitos más saludables en alimentación y ejercicio"},
    "category": {"S": "health"},
    "targetDate": {"S": "2024-06-30"},
    "goals": {"L": [
        {"M": {
            "id": {"S": "goal-003"},
            "title": {"S": "Ejercicio 3 veces por semana"},
            "description": {"S": "Mantener rutina de ejercicio"},
            "completed": {"BOOL": false},
            "priority": {"S": "high"}
        }},
        {"M": {
            "id": {"S": "goal-004"},
            "title": {"S": "Comer más verduras"},
            "description": {"S": "Incluir verduras en cada comida"},
            "completed": {"BOOL": false},
            "priority": {"S": "medium"}
        }}
    ]},
    "progress": {"N": "10"},
    "status": {"S": "active"},
    "createdAt": {"S": "2024-01-05T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-05T00:00:00.000Z"}
}' "Plan vida saludable"

# Plan de vida 3: Relaciones
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "LIFEPLAN#plan-003"},
    "GSI1PK": {"S": "LIFEPLAN"},
    "GSI1SK": {"S": "plan-003"},
    "id": {"S": "plan-003"},
    "userId": {"S": "user-456"},
    "title": {"S": "Fortalecer relaciones"},
    "description": {"S": "Mejorar la comunicación y conexión con seres queridos"},
    "category": {"S": "relationships"},
    "targetDate": {"S": "2024-08-31"},
    "goals": {"L": [
        {"M": {
            "id": {"S": "goal-005"},
            "title": {"S": "Cenas románticas semanales"},
            "description": {"S": "Cocinar juntos una vez por semana"},
            "completed": {"BOOL": true},
            "priority": {"S": "high"}
        }}
    ]},
    "progress": {"N": "60"},
    "status": {"S": "active"},
    "createdAt": {"S": "2024-01-08T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-12T00:00:00.000Z"}
}' "Plan fortalecer relaciones"

echo ""
echo "⏰ Insertando programaciones de prueba..."

# Programación 1: Regar árboles
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SCHEDULE#schedule-001"},
    "GSI1PK": {"S": "SCHEDULE"},
    "GSI1SK": {"S": "schedule-001"},
    "id": {"S": "schedule-001"},
    "userId": {"S": "user-123"},
    "title": {"S": "Regar árboles"},
    "description": {"S": "Recordatorio diario para regar los árboles virtuales"},
    "type": {"S": "reminder"},
    "scheduledTime": {"S": "2024-01-15T08:00:00.000Z"},
    "recurrence": {"S": "daily"},
    "action": {"M": {
        "type": {"S": "water_tree"},
        "data": {"M": {
            "userId": {"S": "user-123"},
            "treeId": {"S": "tree-001"},
            "waterAmount": {"N": "20"}
        }}
    }},
    "isActive": {"BOOL": true},
    "nextTrigger": {"S": "2024-01-15T08:00:00.000Z"},
    "lastTriggered": {"S": "2024-01-14T08:00:00.000Z"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-14T08:00:00.000Z"}
}' "Programación regar árboles"

# Programación 2: Pregunta diaria
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SCHEDULE#schedule-002"},
    "GSI1PK": {"S": "SCHEDULE"},
    "GSI1SK": {"S": "schedule-002"},
    "id": {"S": "schedule-002"},
    "userId": {"S": "user-123"},
    "title": {"S": "Pregunta diaria"},
    "description": {"S": "Notificación para responder la pregunta del día"},
    "type": {"S": "notification"},
    "scheduledTime": {"S": "2024-01-15T09:00:00.000Z"},
    "recurrence": {"S": "daily"},
    "action": {"M": {
        "type": {"S": "notification"},
        "data": {"M": {
            "title": {"S": "Pregunta del día"},
            "message": {"S": "¡Hay una nueva pregunta esperándote!"}
        }}
    }},
    "isActive": {"BOOL": true},
    "nextTrigger": {"S": "2024-01-15T09:00:00.000Z"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Programación pregunta diaria"

# Programación 3: Recordatorio de ejercicio
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SCHEDULE#schedule-003"},
    "GSI1PK": {"S": "SCHEDULE"},
    "GSI1SK": {"S": "schedule-003"},
    "id": {"S": "schedule-003"},
    "userId": {"S": "user-123"},
    "title": {"S": "Tiempo de ejercicio"},
    "description": {"S": "Recordatorio para hacer ejercicio"},
    "type": {"S": "reminder"},
    "scheduledTime": {"S": "2024-01-15T18:00:00.000Z"},
    "recurrence": {"S": "weekly"},
    "action": {"M": {
        "type": {"S": "reminder"},
        "data": {"M": {
            "title": {"S": "¡Hora de ejercitarse!"},
            "message": {"S": "Es momento de tu rutina de ejercicio"}
        }}
    }},
    "isActive": {"BOOL": true},
    "nextTrigger": {"S": "2024-01-15T18:00:00.000Z"},
    "createdAt": {"S": "2024-01-05T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-05T00:00:00.000Z"}
}' "Programación ejercicio"

echo ""
echo "⭐ Insertando favoritos de prueba..."

# Favorito 1: Receta favorita
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "FAVORITE#fav-001"},
    "id": {"S": "fav-001"},
    "userId": {"S": "user-123"},
    "itemId": {"S": "recipe-001"},
    "itemType": {"S": "recipe"},
    "title": {"S": "Pasta Carbonara"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Favorito Pasta Carbonara"

# Favorito 2: Ingrediente favorito
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "FAVORITE#fav-002"},
    "id": {"S": "fav-002"},
    "userId": {"S": "user-123"},
    "itemId": {"S": "ingredient-002"},
    "itemType": {"S": "ingredient"},
    "title": {"S": "Pollo"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Favorito Pollo"

# Favorito 3: Pregunta favorita
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "FAVORITE#fav-003"},
    "id": {"S": "fav-003"},
    "userId": {"S": "user-456"},
    "itemId": {"S": "question-001"},
    "itemType": {"S": "question"},
    "title": {"S": "¿Cuál es tu comida favorita y por qué?"},
    "createdAt": {"S": "2024-01-04T00:00:00.000Z"}
}' "Favorito pregunta comida"

echo ""
echo "🎉 ¡Población de datos completada!"
echo ""
echo "📊 Resumen de datos insertados:"
echo "   ✅ 3 usuarios de prueba"
echo "   ✅ 3 recetas completas"
echo "   ✅ 3 ingredientes con información nutricional"
echo "   ✅ 3 listas de compras"
echo "   ✅ 3 árboles virtuales"
echo "   ✅ 3 relaciones de pareja"
echo "   ✅ 3 preguntas (abierta y múltiple)"
echo "   ✅ 3 planes de vida con metas"
echo "   ✅ 3 programaciones automáticas"
echo "   ✅ 3 elementos favoritos"
echo ""
echo "📋 Total: 30 registros insertados en DynamoDB"
echo ""
echo "🔍 Para verificar los datos insertados, ejecuta:"
echo "   aws dynamodb scan --table-name $TABLE_NAME --limit 10 --region $REGION"
echo ""
echo "🚀 ¡Tu tabla DynamoDB está lista para probar HopieApp!"
