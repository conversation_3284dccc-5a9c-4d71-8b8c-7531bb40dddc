#!/bin/bash

# 🚀 Script para poblar DynamoDB con datos de prueba para HopieApp
# Inserta datos de prueba para todas las entidades

TABLE_NAME="hopie-main-table-dev"
REGION="us-east-2"

echo "📊 Poblando tabla DynamoDB con datos de prueba..."
echo "📋 Tabla: $TABLE_NAME"
echo "🌍 Región: $REGION"
echo ""

# Función para insertar un item individual
insert_item() {
    local item_json="$1"
    local description="$2"
    
    echo "🔄 Insertando: $description"
    
    aws dynamodb put-item \
        --table-name $TABLE_NAME \
        --item "$item_json" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $description insertado exitosamente"
    else
        echo "   ❌ Error insertando $description"
    fi
}

echo "👤 Insertando usuarios de prueba..."

# Usuario 1: <PERSON>
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "PROFILE"},
    "id": {"S": "user-123"},
    "name": {"S": "<PERSON>"},
    "email": {"S": "<EMAIL>"},
    "phone": {"S": "+1234567890"},
    "birthDate": {"S": "1990-05-15"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Usuario Juan Pérez"

# Usuario 2: María García
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "PROFILE"},
    "id": {"S": "user-456"},
    "name": {"S": "María García"},
    "email": {"S": "<EMAIL>"},
    "phone": {"S": "+1234567891"},
    "birthDate": {"S": "1992-08-22"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Usuario María García"

# Usuario 3: Carlos López
insert_item '{
    "PK": {"S": "USER#user-789"},
    "SK": {"S": "PROFILE"},
    "id": {"S": "user-789"},
    "name": {"S": "Carlos López"},
    "email": {"S": "<EMAIL>"},
    "phone": {"S": "+1234567892"},
    "birthDate": {"S": "1988-12-10"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Usuario Carlos López"

echo ""
echo "📍 Insertando lugares de prueba..."

# Lugar 1: Restaurante romántico
insert_item '{
    "PK": {"S": "PLACE#place-001"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "PLACE"},
    "GSI1SK": {"S": "place-001"},
    "id": {"S": "place-001"},
    "name": {"S": "Restaurante La Terraza"},
    "description": {"S": "Restaurante romántico con vista al mar"},
    "category": {"S": "restaurant"},
    "address": {"S": "Av. Costanera 123, Miami Beach"},
    "coordinates": {"M": {
        "lat": {"N": "25.7617"},
        "lng": {"N": "-80.1918"}
    }},
    "website": {"S": "https://laterraza.com"},
    "phone": {"S": "******-555-0123"},
    "rating": {"N": "4.5"},
    "reviewCount": {"N": "127"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Lugar Restaurante La Terraza"

# Lugar 2: Parque para citas
insert_item '{
    "PK": {"S": "PLACE#place-002"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "PLACE"},
    "GSI1SK": {"S": "place-002"},
    "id": {"S": "place-002"},
    "name": {"S": "Parque Central"},
    "description": {"S": "Hermoso parque ideal para caminatas románticas"},
    "category": {"S": "park"},
    "address": {"S": "Central Park Ave, New York"},
    "coordinates": {"M": {
        "lat": {"N": "40.7829"},
        "lng": {"N": "-73.9654"}
    }},
    "website": {"S": "https://centralpark.com"},
    "phone": {"S": "******-555-0456"},
    "rating": {"N": "4.8"},
    "reviewCount": {"N": "2341"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Lugar Parque Central"

# Lugar 3: Café acogedor
insert_item '{
    "PK": {"S": "PLACE#place-003"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "PLACE"},
    "GSI1SK": {"S": "place-003"},
    "id": {"S": "place-003"},
    "name": {"S": "Café Aroma"},
    "description": {"S": "Café acogedor perfecto para conversaciones íntimas"},
    "category": {"S": "cafe"},
    "address": {"S": "Main St 456, Los Angeles"},
    "coordinates": {"M": {
        "lat": {"N": "34.0522"},
        "lng": {"N": "-118.2437"}
    }},
    "website": {"S": "https://cafearoma.com"},
    "phone": {"S": "******-555-0789"},
    "rating": {"N": "4.3"},
    "reviewCount": {"N": "89"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Lugar Café Aroma"

echo ""
echo "📝 Insertando categorías de prueba..."

# Categoría 1: Lugares románticos
insert_item '{
    "PK": {"S": "CATEGORY#cat-001"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "CATEGORY"},
    "GSI1SK": {"S": "cat-001"},
    "id": {"S": "cat-001"},
    "name": {"S": "Lugares Románticos"},
    "description": {"S": "Lugares perfectos para citas románticas"},
    "type": {"S": "place"},
    "color": {"S": "#FF69B4"},
    "icon": {"S": "heart"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Categoría Lugares Románticos"

# Categoría 2: Actividades en pareja
insert_item '{
    "PK": {"S": "CATEGORY#cat-002"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "CATEGORY"},
    "GSI1SK": {"S": "cat-002"},
    "id": {"S": "cat-002"},
    "name": {"S": "Actividades en Pareja"},
    "description": {"S": "Actividades divertidas para hacer juntos"},
    "type": {"S": "activity"},
    "color": {"S": "#32CD32"},
    "icon": {"S": "activity"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Categoría Actividades en Pareja"

# Categoría 3: Preguntas íntimas
insert_item '{
    "PK": {"S": "CATEGORY#cat-003"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "CATEGORY"},
    "GSI1SK": {"S": "cat-003"},
    "id": {"S": "cat-003"},
    "name": {"S": "Preguntas Íntimas"},
    "description": {"S": "Preguntas para conocerse mejor"},
    "type": {"S": "question"},
    "color": {"S": "#9370DB"},
    "icon": {"S": "question"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Categoría Preguntas Íntimas"

echo ""
echo "💬 Insertando salas de chat de prueba..."

# Sala de chat 1: Pareja principal
insert_item '{
    "PK": {"S": "ROOM#room-001"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "ROOM"},
    "GSI1SK": {"S": "room-001"},
    "roomId": {"S": "room-001"},
    "name": {"S": "Juan & María"},
    "participants": {"L": [
        {"S": "user-123"},
        {"S": "user-456"}
    ]},
    "type": {"S": "couple"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Sala de chat Juan & María"

# Sala de chat 2: Grupo de amigos
insert_item '{
    "PK": {"S": "ROOM#room-002"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "ROOM"},
    "GSI1SK": {"S": "room-002"},
    "roomId": {"S": "room-002"},
    "name": {"S": "Amigos HopieApp"},
    "participants": {"L": [
        {"S": "user-123"},
        {"S": "user-456"},
        {"S": "user-789"}
    ]},
    "type": {"S": "group"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Sala de chat Amigos"

# Sala de chat 3: Chat privado
insert_item '{
    "PK": {"S": "ROOM#room-003"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "ROOM"},
    "GSI1SK": {"S": "room-003"},
    "roomId": {"S": "room-003"},
    "name": {"S": "Carlos & Ana"},
    "participants": {"L": [
        {"S": "user-789"},
        {"S": "user-101"}
    ]},
    "type": {"S": "private"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Sala de chat Carlos & Ana"

echo ""
echo "🌳 Insertando árboles de prueba..."

# Árbol 1: Roble de la Esperanza
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "TREE#tree-001"},
    "GSI1PK": {"S": "TREE"},
    "GSI1SK": {"S": "tree-001"},
    "id": {"S": "tree-001"},
    "userId": {"S": "user-123"},
    "name": {"S": "Roble de la Esperanza"},
    "type": {"S": "oak"},
    "location": {"S": "Jardín principal"},
    "plantedDate": {"S": "2024-01-01T00:00:00.000Z"},
    "growthLevel": {"N": "3"},
    "waterLevel": {"N": "85"},
    "health": {"N": "95"},
    "lastWatered": {"S": "2024-01-10T08:00:00.000Z"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-10T08:00:00.000Z"}
}' "Árbol Roble de la Esperanza"

# Árbol 2: Cerezo del Amor
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "TREE#tree-002"},
    "GSI1PK": {"S": "TREE"},
    "GSI1SK": {"S": "tree-002"},
    "id": {"S": "tree-002"},
    "userId": {"S": "user-123"},
    "name": {"S": "Cerezo del Amor"},
    "type": {"S": "cherry"},
    "location": {"S": "Patio trasero"},
    "plantedDate": {"S": "2024-01-05T00:00:00.000Z"},
    "growthLevel": {"N": "2"},
    "waterLevel": {"N": "70"},
    "health": {"N": "88"},
    "lastWatered": {"S": "2024-01-09T18:00:00.000Z"},
    "createdAt": {"S": "2024-01-05T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-09T18:00:00.000Z"}
}' "Árbol Cerezo del Amor"

# Árbol 3: Pino de la Sabiduría
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "TREE#tree-003"},
    "GSI1PK": {"S": "TREE"},
    "GSI1SK": {"S": "tree-003"},
    "id": {"S": "tree-003"},
    "userId": {"S": "user-123"},
    "name": {"S": "Pino de la Sabiduría"},
    "type": {"S": "pine"},
    "location": {"S": "Entrada principal"},
    "plantedDate": {"S": "2024-01-08T00:00:00.000Z"},
    "growthLevel": {"N": "1"},
    "waterLevel": {"N": "60"},
    "health": {"N": "92"},
    "lastWatered": {"S": "2024-01-11T07:30:00.000Z"},
    "createdAt": {"S": "2024-01-08T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-11T07:30:00.000Z"}
}' "Árbol Pino de la Sabiduría"

echo ""
echo "❤️ Insertando relaciones de pareja de prueba..."

# Relación de pareja 1
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "COUPLE#couple-001"},
    "GSI1PK": {"S": "COUPLE"},
    "GSI1SK": {"S": "couple-001"},
    "id": {"S": "couple-001"},
    "userId": {"S": "user-123"},
    "partnerEmail": {"S": "<EMAIL>"},
    "partnerUserId": {"S": "user-456"},
    "relationshipType": {"S": "dating"},
    "startDate": {"S": "2023-12-01"},
    "status": {"S": "active"},
    "createdAt": {"S": "2023-12-01T00:00:00.000Z"},
    "updatedAt": {"S": "2023-12-01T00:00:00.000Z"}
}' "Relación Juan y María"

# Relación de pareja 2
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "COUPLE#couple-002"},
    "GSI1PK": {"S": "COUPLE"},
    "GSI1SK": {"S": "couple-002"},
    "id": {"S": "couple-002"},
    "userId": {"S": "user-456"},
    "partnerEmail": {"S": "<EMAIL>"},
    "partnerUserId": {"S": "user-123"},
    "relationshipType": {"S": "dating"},
    "startDate": {"S": "2023-12-01"},
    "status": {"S": "active"},
    "createdAt": {"S": "2023-12-01T00:00:00.000Z"},
    "updatedAt": {"S": "2023-12-01T00:00:00.000Z"}
}' "Relación María y Juan"

# Relación pendiente
insert_item '{
    "PK": {"S": "USER#user-789"},
    "SK": {"S": "COUPLE#couple-003"},
    "GSI1PK": {"S": "COUPLE"},
    "GSI1SK": {"S": "couple-003"},
    "id": {"S": "couple-003"},
    "userId": {"S": "user-789"},
    "partnerEmail": {"S": "<EMAIL>"},
    "relationshipType": {"S": "friendship"},
    "startDate": {"S": "2024-01-10"},
    "status": {"S": "pending"},
    "createdAt": {"S": "2024-01-10T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-10T00:00:00.000Z"}
}' "Relación pendiente Carlos"

echo ""
echo "❓ Insertando preguntas de prueba..."

# Pregunta 1: Abierta sobre sueños
insert_item '{
    "PK": {"S": "QUESTION#question-001"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "QUESTION"},
    "GSI1SK": {"S": "question-001"},
    "id": {"S": "question-001"},
    "text": {"S": "¿Cuál es tu mayor sueño en la vida y qué estás haciendo para alcanzarlo?"},
    "category": {"S": "dreams"},
    "type": {"S": "open"},
    "difficulty": {"S": "easy"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Pregunta sobre sueños"

# Pregunta 2: Múltiple sobre relaciones
insert_item '{
    "PK": {"S": "QUESTION#question-002"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "QUESTION"},
    "GSI1SK": {"S": "question-002"},
    "id": {"S": "question-002"},
    "text": {"S": "¿Qué es lo más importante en una relación de pareja?"},
    "category": {"S": "relationships"},
    "type": {"S": "multiple_choice"},
    "options": {"L": [
        {"S": "Comunicación"},
        {"S": "Confianza"},
        {"S": "Respeto"},
        {"S": "Amor"}
    ]},
    "correctAnswer": {"S": "Comunicación"},
    "difficulty": {"S": "medium"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Pregunta sobre relaciones"

# Pregunta 3: Múltiple sobre citas
insert_item '{
    "PK": {"S": "QUESTION#question-003"},
    "SK": {"S": "METADATA"},
    "GSI1PK": {"S": "QUESTION"},
    "GSI1SK": {"S": "question-003"},
    "id": {"S": "question-003"},
    "text": {"S": "¿Cuál consideras la cita perfecta?"},
    "category": {"S": "dating"},
    "type": {"S": "multiple_choice"},
    "options": {"L": [
        {"S": "Cena romántica"},
        {"S": "Caminata en el parque"},
        {"S": "Noche de películas en casa"},
        {"S": "Aventura al aire libre"}
    ]},
    "correctAnswer": {"S": "Caminata en el parque"},
    "difficulty": {"S": "easy"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Pregunta sobre citas"

echo ""
echo "🎯 Insertando planes de vida de prueba..."

# Plan de vida 1: Fortalecer relación de pareja
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "LIFEPLAN#plan-001"},
    "GSI1PK": {"S": "LIFEPLAN"},
    "GSI1SK": {"S": "plan-001"},
    "id": {"S": "plan-001"},
    "userId": {"S": "user-123"},
    "title": {"S": "Fortalecer relación de pareja"},
    "description": {"S": "Mejorar la comunicación y conexión con mi pareja"},
    "category": {"S": "relationships"},
    "targetDate": {"S": "2024-12-31"},
    "goals": {"L": [
        {"M": {
            "id": {"S": "goal-001"},
            "title": {"S": "Citas semanales"},
            "description": {"S": "Tener una cita romántica cada semana"},
            "completed": {"BOOL": true},
            "priority": {"S": "high"}
        }},
        {"M": {
            "id": {"S": "goal-002"},
            "title": {"S": "Comunicación diaria"},
            "description": {"S": "Hablar 30 minutos sin distracciones cada día"},
            "completed": {"BOOL": false},
            "priority": {"S": "high"}
        }}
    ]},
    "progress": {"N": "40"},
    "status": {"S": "active"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-10T00:00:00.000Z"}
}' "Plan fortalecer relación"

# Plan de vida 2: Crecimiento personal
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "LIFEPLAN#plan-002"},
    "GSI1PK": {"S": "LIFEPLAN"},
    "GSI1SK": {"S": "plan-002"},
    "id": {"S": "plan-002"},
    "userId": {"S": "user-123"},
    "title": {"S": "Crecimiento personal"},
    "description": {"S": "Desarrollar nuevas habilidades y conocerme mejor"},
    "category": {"S": "personal_growth"},
    "targetDate": {"S": "2024-06-30"},
    "goals": {"L": [
        {"M": {
            "id": {"S": "goal-003"},
            "title": {"S": "Leer 12 libros"},
            "description": {"S": "Leer un libro por mes"},
            "completed": {"BOOL": false},
            "priority": {"S": "medium"}
        }},
        {"M": {
            "id": {"S": "goal-004"},
            "title": {"S": "Meditar diariamente"},
            "description": {"S": "10 minutos de meditación cada día"},
            "completed": {"BOOL": false},
            "priority": {"S": "high"}
        }}
    ]},
    "progress": {"N": "15"},
    "status": {"S": "active"},
    "createdAt": {"S": "2024-01-05T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-05T00:00:00.000Z"}
}' "Plan crecimiento personal"

# Plan de vida 3: Relaciones
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "LIFEPLAN#plan-003"},
    "GSI1PK": {"S": "LIFEPLAN"},
    "GSI1SK": {"S": "plan-003"},
    "id": {"S": "plan-003"},
    "userId": {"S": "user-456"},
    "title": {"S": "Fortalecer relaciones"},
    "description": {"S": "Mejorar la comunicación y conexión con seres queridos"},
    "category": {"S": "relationships"},
    "targetDate": {"S": "2024-08-31"},
    "goals": {"L": [
        {"M": {
            "id": {"S": "goal-005"},
            "title": {"S": "Cenas románticas semanales"},
            "description": {"S": "Cocinar juntos una vez por semana"},
            "completed": {"BOOL": true},
            "priority": {"S": "high"}
        }}
    ]},
    "progress": {"N": "60"},
    "status": {"S": "active"},
    "createdAt": {"S": "2024-01-08T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-12T00:00:00.000Z"}
}' "Plan fortalecer relaciones"

echo ""
echo "⏰ Insertando programaciones de prueba..."

# Programación 1: Regar árboles
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SCHEDULE#schedule-001"},
    "GSI1PK": {"S": "SCHEDULE"},
    "GSI1SK": {"S": "schedule-001"},
    "id": {"S": "schedule-001"},
    "userId": {"S": "user-123"},
    "title": {"S": "Regar árboles"},
    "description": {"S": "Recordatorio diario para regar los árboles virtuales"},
    "type": {"S": "reminder"},
    "scheduledTime": {"S": "2024-01-15T08:00:00.000Z"},
    "recurrence": {"S": "daily"},
    "action": {"M": {
        "type": {"S": "water_tree"},
        "data": {"M": {
            "userId": {"S": "user-123"},
            "treeId": {"S": "tree-001"},
            "waterAmount": {"N": "20"}
        }}
    }},
    "isActive": {"BOOL": true},
    "nextTrigger": {"S": "2024-01-15T08:00:00.000Z"},
    "lastTriggered": {"S": "2024-01-14T08:00:00.000Z"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-14T08:00:00.000Z"}
}' "Programación regar árboles"

# Programación 2: Pregunta diaria
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SCHEDULE#schedule-002"},
    "GSI1PK": {"S": "SCHEDULE"},
    "GSI1SK": {"S": "schedule-002"},
    "id": {"S": "schedule-002"},
    "userId": {"S": "user-123"},
    "title": {"S": "Pregunta diaria"},
    "description": {"S": "Notificación para responder la pregunta del día"},
    "type": {"S": "notification"},
    "scheduledTime": {"S": "2024-01-15T09:00:00.000Z"},
    "recurrence": {"S": "daily"},
    "action": {"M": {
        "type": {"S": "notification"},
        "data": {"M": {
            "title": {"S": "Pregunta del día"},
            "message": {"S": "¡Hay una nueva pregunta esperándote!"}
        }}
    }},
    "isActive": {"BOOL": true},
    "nextTrigger": {"S": "2024-01-15T09:00:00.000Z"},
    "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
}' "Programación pregunta diaria"

# Programación 3: Recordatorio de ejercicio
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "SCHEDULE#schedule-003"},
    "GSI1PK": {"S": "SCHEDULE"},
    "GSI1SK": {"S": "schedule-003"},
    "id": {"S": "schedule-003"},
    "userId": {"S": "user-123"},
    "title": {"S": "Tiempo de ejercicio"},
    "description": {"S": "Recordatorio para hacer ejercicio"},
    "type": {"S": "reminder"},
    "scheduledTime": {"S": "2024-01-15T18:00:00.000Z"},
    "recurrence": {"S": "weekly"},
    "action": {"M": {
        "type": {"S": "reminder"},
        "data": {"M": {
            "title": {"S": "¡Hora de ejercitarse!"},
            "message": {"S": "Es momento de tu rutina de ejercicio"}
        }}
    }},
    "isActive": {"BOOL": true},
    "nextTrigger": {"S": "2024-01-15T18:00:00.000Z"},
    "createdAt": {"S": "2024-01-05T00:00:00.000Z"},
    "updatedAt": {"S": "2024-01-05T00:00:00.000Z"}
}' "Programación ejercicio"

echo ""
echo "⭐ Insertando favoritos de prueba..."

# Favorito 1: Lugar favorito
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "FAVORITE#fav-001"},
    "id": {"S": "fav-001"},
    "userId": {"S": "user-123"},
    "itemId": {"S": "place-001"},
    "itemType": {"S": "place"},
    "title": {"S": "Restaurante La Terraza"},
    "createdAt": {"S": "2024-01-02T00:00:00.000Z"}
}' "Favorito Restaurante La Terraza"

# Favorito 2: Pregunta favorita
insert_item '{
    "PK": {"S": "USER#user-123"},
    "SK": {"S": "FAVORITE#fav-002"},
    "id": {"S": "fav-002"},
    "userId": {"S": "user-123"},
    "itemId": {"S": "question-001"},
    "itemType": {"S": "question"},
    "title": {"S": "¿Cuál es tu mayor sueño en la vida?"},
    "createdAt": {"S": "2024-01-03T00:00:00.000Z"}
}' "Favorito pregunta sueños"

# Favorito 3: Plan de vida favorito
insert_item '{
    "PK": {"S": "USER#user-456"},
    "SK": {"S": "FAVORITE#fav-003"},
    "id": {"S": "fav-003"},
    "userId": {"S": "user-456"},
    "itemId": {"S": "plan-003"},
    "itemType": {"S": "lifeplan"},
    "title": {"S": "Fortalecer relaciones"},
    "createdAt": {"S": "2024-01-04T00:00:00.000Z"}
}' "Favorito plan relaciones"

echo ""
echo "🎉 ¡Población de datos completada!"
echo ""
echo "📊 Resumen de datos insertados:"
echo "   ✅ 3 usuarios de prueba"
echo "   ✅ 3 lugares románticos (restaurante, parque, café)"
echo "   ✅ 3 categorías (lugares, actividades, preguntas)"
echo "   ✅ 3 salas de chat (pareja, grupo, privado)"
echo "   ✅ 3 árboles virtuales"
echo "   ✅ 3 relaciones de pareja"
echo "   ✅ 3 preguntas íntimas y de pareja"
echo "   ✅ 3 planes de vida con metas"
echo "   ✅ 3 programaciones automáticas"
echo "   ✅ 3 elementos favoritos"
echo ""
echo "📋 Total: 30 registros insertados en DynamoDB"
echo ""
echo "🔍 Para verificar los datos insertados, ejecuta:"
echo "   aws dynamodb scan --table-name $TABLE_NAME --limit 10 --region $REGION"
echo ""
echo "🚀 ¡Tu tabla DynamoDB está lista para probar HopieApp!"
