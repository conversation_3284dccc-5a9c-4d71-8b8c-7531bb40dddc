const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('WebSocket Connect Event:', JSON.stringify(event, null, 2));

    const { requestContext } = event;
    const { connectionId } = requestContext;
    const userId = event.queryStringParameters?.userId || 'anonymous';

    try {
        const params = {
            TableName: TABLE_NAME,
            Item: {
                PK: `CONNECTION#${connectionId}`,
                SK: 'METADATA',
                connectionId,
                userId,
                connectedAt: new Date().toISOString(),
                status: 'connected',
                lastActivity: new Date().toISOString()
            }
        };

        await dynamodb.put(params).promise();

        console.log(`User ${userId} connected with connection ${connectionId}`);

        return {
            statusCode: 200,
            body: 'Connected'
        };
    } catch (error) {
        console.error('Error in connect:', error);
        return {
            statusCode: 500,
            body: 'Failed to connect'
        };
    }
};
