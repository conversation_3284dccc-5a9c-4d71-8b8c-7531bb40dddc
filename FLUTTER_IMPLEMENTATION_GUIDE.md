# 📱 Guía de Implementación Flutter para HopieApp

## 🔧 Configuración Inicial

### Dependencias requeridas en `pubspec.yaml`:
```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0
  amazon_cognito_identity_dart_2: ^3.0.1
  web_socket_channel: ^2.4.0
  shared_preferences: ^2.2.2
  provider: ^6.1.1
  dio: ^5.3.2
  json_annotation: ^4.8.1

dev_dependencies:
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
```

## 🌐 Configuración de URLs Base

```dart
class ApiConfig {
  static const String REST_API_BASE = 'https://ovxbfj0noa.execute-api.us-east-2.amazonaws.com/dev';
  static const String WEBSOCKET_URL = 'wss://w7xk45by82.execute-api.us-east-2.amazonaws.com/dev/';
  
  // Cognito Configuration
  static const String USER_POOL_ID = 'us-east-2_gTZjXep7j';
  static const String CLIENT_ID = '63604alcg1rvili3ksfan61fid';
  static const String REGION = 'us-east-2';
}
```

## 🔐 1. AUTENTICACIÓN CON COGNITO

### Configuración del servicio de autenticación:

```dart
import 'package:amazon_cognito_identity_dart_2/cognito.dart';

class AuthService {
  late CognitoUserPool _userPool;
  late CognitoUser? _cognitoUser;
  CognitoUserSession? _session;

  AuthService() {
    _userPool = CognitoUserPool(
      ApiConfig.USER_POOL_ID,
      ApiConfig.CLIENT_ID,
    );
  }

  // 1.1 REGISTRO DE USUARIO
  Future<Map<String, dynamic>> signUp({
    required String email,
    required String password,
    required String name,
    required String phone,
  }) async {
    try {
      final userAttributes = [
        AttributeArg(name: 'email', value: email),
        AttributeArg(name: 'name', value: name),
        AttributeArg(name: 'phone_number', value: phone),
      ];

      final result = await _userPool.signUp(
        email,
        password,
        userAttributes: userAttributes,
      );

      return {
        'success': true,
        'userSub': result.userSub,
        'codeDeliveryDetails': result.codeDeliveryDetails,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 1.2 CONFIRMACIÓN DE CÓDIGO
  Future<Map<String, dynamic>> confirmSignUp({
    required String email,
    required String confirmationCode,
  }) async {
    try {
      _cognitoUser = CognitoUser(email, _userPool);
      final result = await _cognitoUser!.confirmRegistration(confirmationCode);
      
      return {'success': true, 'confirmed': result};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 1.3 INICIO DE SESIÓN
  Future<Map<String, dynamic>> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _cognitoUser = CognitoUser(email, _userPool);
      final authDetails = AuthenticationDetails(
        username: email,
        password: password,
      );

      _session = await _cognitoUser!.authenticateUser(authDetails);
      
      return {
        'success': true,
        'accessToken': _session!.getAccessToken().getJwtToken(),
        'idToken': _session!.getIdToken().getJwtToken(),
        'refreshToken': _session!.getRefreshToken()?.getToken(),
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 1.4 CERRAR SESIÓN
  Future<void> signOut() async {
    if (_cognitoUser != null) {
      await _cognitoUser!.signOut();
      _session = null;
    }
  }

  // 1.5 OBTENER TOKEN ACTUAL
  String? getCurrentToken() {
    return _session?.getIdToken().getJwtToken();
  }

  // 1.6 VERIFICAR SI ESTÁ AUTENTICADO
  bool isAuthenticated() {
    return _session != null && _session!.isValid();
  }
}
```

## 🌐 2. SERVICIO HTTP BASE

```dart
import 'package:dio/dio.dart';

class ApiService {
  late Dio _dio;
  final AuthService _authService;

  ApiService(this._authService) {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.REST_API_BASE,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
    ));

    // Interceptor para agregar token automáticamente
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        final token = _authService.getCurrentToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        options.headers['Content-Type'] = 'application/json';
        handler.next(options);
      },
      onError: (error, handler) {
        if (error.response?.statusCode == 401) {
          // Token expirado, redirigir a login
          _authService.signOut();
        }
        handler.next(error);
      },
    ));
  }

  // Métodos HTTP genéricos
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) {
    return _dio.get(path, queryParameters: queryParameters);
  }

  Future<Response> post(String path, {dynamic data}) {
    return _dio.post(path, data: data);
  }

  Future<Response> put(String path, {dynamic data}) {
    return _dio.put(path, data: data);
  }

  Future<Response> delete(String path) {
    return _dio.delete(path);
  }
}
```

## 👤 3. GESTIÓN DE USUARIOS

```dart
class UserService {
  final ApiService _apiService;

  UserService(this._apiService);

  // 3.1 OBTENER PERFIL DE USUARIO
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final response = await _apiService.get('/user/profile');
      return {
        'success': true,
        'user': response.data['user'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 3.2 ACTUALIZAR PERFIL
  Future<Map<String, dynamic>> updateProfile({
    required String name,
    required String email,
    String? phone,
    String? birthDate,
  }) async {
    try {
      final response = await _apiService.put('/user/profile', data: {
        'name': name,
        'email': email,
        'phone': phone,
        'birthDate': birthDate,
      });
      
      return {
        'success': true,
        'user': response.data['user'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 3.3 OBTENER PREFERENCIAS
  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      final response = await _apiService.get('/user/preferences');
      return {
        'success': true,
        'preferences': response.data['preferences'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 3.4 ACTUALIZAR PREFERENCIAS
  Future<Map<String, dynamic>> updatePreferences({
    required bool notifications,
    required String theme,
    required String language,
  }) async {
    try {
      final response = await _apiService.put('/user/preferences', data: {
        'notifications': notifications,
        'theme': theme,
        'language': language,
      });
      
      return {
        'success': true,
        'preferences': response.data['preferences'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
```

## 🍳 4. GESTIÓN DE RECETAS

```dart
class RecipeService {
  final ApiService _apiService;

  RecipeService(this._apiService);

  // 4.1 OBTENER TODAS LAS RECETAS
  Future<Map<String, dynamic>> getRecipes() async {
    try {
      final response = await _apiService.get('/recipes');
      return {
        'success': true,
        'recipes': response.data['recipes'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 4.2 OBTENER RECETA POR ID
  Future<Map<String, dynamic>> getRecipeById(String recipeId) async {
    try {
      final response = await _apiService.get('/recipe/$recipeId');
      return {
        'success': true,
        'recipe': response.data['recipe'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 4.3 CREAR NUEVA RECETA
  Future<Map<String, dynamic>> createRecipe({
    required String title,
    required String description,
    required List<String> ingredients,
    required List<String> instructions,
    required int cookingTime,
    required String difficulty,
  }) async {
    try {
      final response = await _apiService.post('/recipes', data: {
        'title': title,
        'description': description,
        'ingredients': ingredients,
        'instructions': instructions,
        'cookingTime': cookingTime,
        'difficulty': difficulty,
      });
      
      return {
        'success': true,
        'recipe': response.data['recipe'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 4.4 ACTUALIZAR RECETA
  Future<Map<String, dynamic>> updateRecipe({
    required String recipeId,
    required String title,
    required String description,
    required List<String> ingredients,
    required List<String> instructions,
    required int cookingTime,
    required String difficulty,
  }) async {
    try {
      final response = await _apiService.put('/recipe/$recipeId', data: {
        'title': title,
        'description': description,
        'ingredients': ingredients,
        'instructions': instructions,
        'cookingTime': cookingTime,
        'difficulty': difficulty,
      });
      
      return {
        'success': true,
        'recipe': response.data['recipe'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 4.5 ELIMINAR RECETA
  Future<Map<String, dynamic>> deleteRecipe(String recipeId) async {
    try {
      await _apiService.delete('/recipe/$recipeId');
      return {'success': true};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
```

## 🥕 5. GESTIÓN DE INGREDIENTES

```dart
class IngredientService {
  final ApiService _apiService;

  IngredientService(this._apiService);

  // 5.1 OBTENER TODOS LOS INGREDIENTES
  Future<Map<String, dynamic>> getIngredients() async {
    try {
      final response = await _apiService.get('/ingredients');
      return {
        'success': true,
        'ingredients': response.data['ingredients'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 5.2 CREAR INGREDIENTE
  Future<Map<String, dynamic>> createIngredient({
    required String name,
    required String category,
    required String unit,
    Map<String, dynamic>? nutritionalInfo,
  }) async {
    try {
      final response = await _apiService.post('/ingredients', data: {
        'name': name,
        'category': category,
        'unit': unit,
        'nutritionalInfo': nutritionalInfo,
      });
      
      return {
        'success': true,
        'ingredient': response.data['ingredient'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
```

## 🛒 6. LISTAS DE COMPRAS

```dart
class ShoppingListService {
  final ApiService _apiService;

  ShoppingListService(this._apiService);

  // 6.1 OBTENER LISTAS DE COMPRAS
  Future<Map<String, dynamic>> getShoppingLists() async {
    try {
      final response = await _apiService.get('/shopping-lists');
      return {
        'success': true,
        'shoppingLists': response.data['shoppingLists'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 6.2 CREAR LISTA DE COMPRAS
  Future<Map<String, dynamic>> createShoppingList({
    required String name,
    required List<Map<String, dynamic>> items,
  }) async {
    try {
      final response = await _apiService.post('/shopping-lists', data: {
        'name': name,
        'items': items,
      });

      return {
        'success': true,
        'shoppingList': response.data['shoppingList'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 6.3 ACTUALIZAR LISTA DE COMPRAS
  Future<Map<String, dynamic>> updateShoppingList({
    required String listId,
    required String name,
    required List<Map<String, dynamic>> items,
  }) async {
    try {
      final response = await _apiService.put('/shopping-list/$listId', data: {
        'name': name,
        'items': items,
      });

      return {
        'success': true,
        'shoppingList': response.data['shoppingList'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## 🌳 7. GESTIÓN DE ÁRBOLES

```dart
class TreeService {
  final ApiService _apiService;

  TreeService(this._apiService);

  // 7.1 OBTENER ÁRBOLES DEL USUARIO
  Future<Map<String, dynamic>> getTrees() async {
    try {
      final response = await _apiService.get('/trees');
      return {
        'success': true,
        'trees': response.data['trees'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 7.2 CREAR NUEVO ÁRBOL
  Future<Map<String, dynamic>> createTree({
    required String name,
    required String type,
    required String location,
    required String plantedDate,
  }) async {
    try {
      final response = await _apiService.post('/trees', data: {
        'name': name,
        'type': type,
        'location': location,
        'plantedDate': plantedDate,
      });

      return {
        'success': true,
        'tree': response.data['tree'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 7.3 REGAR ÁRBOL
  Future<Map<String, dynamic>> waterTree({
    required String treeId,
    int waterAmount = 20,
  }) async {
    try {
      final response = await _apiService.post('/tree/water', data: {
        'treeId': treeId,
        'waterAmount': waterAmount,
      });

      return {
        'success': true,
        'tree': response.data['tree'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 7.4 OBTENER PROGRESO DE CRECIMIENTO
  Future<Map<String, dynamic>> getTreeGrowth(String treeId) async {
    try {
      final response = await _apiService.get('/tree/growth?treeId=$treeId');
      return {
        'success': true,
        'growth': response.data,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## ❤️ 8. GESTIÓN DE PAREJAS

```dart
class CoupleService {
  final ApiService _apiService;

  CoupleService(this._apiService);

  // 8.1 OBTENER RELACIONES
  Future<Map<String, dynamic>> getCouples() async {
    try {
      final response = await _apiService.get('/couples');
      return {
        'success': true,
        'couples': response.data['couples'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 8.2 CREAR RELACIÓN
  Future<Map<String, dynamic>> createCouple({
    required String partnerEmail,
    required String relationshipType,
    required String startDate,
  }) async {
    try {
      final response = await _apiService.post('/couples', data: {
        'partnerEmail': partnerEmail,
        'relationshipType': relationshipType,
        'startDate': startDate,
      });

      return {
        'success': true,
        'couple': response.data['couple'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 8.3 INVITAR PAREJA
  Future<Map<String, dynamic>> invitePartner({
    required String partnerEmail,
    String? message,
  }) async {
    try {
      final response = await _apiService.post('/couple/invite', data: {
        'partnerEmail': partnerEmail,
        'message': message,
      });

      return {
        'success': true,
        'invitation': response.data,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 8.4 ACEPTAR INVITACIÓN
  Future<Map<String, dynamic>> acceptInvitation({
    required String coupleId,
    required bool accept,
  }) async {
    try {
      final response = await _apiService.post('/couple/accept', data: {
        'coupleId': coupleId,
        'accept': accept,
      });

      return {
        'success': true,
        'result': response.data,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
```

## ❓ 9. PREGUNTAS Y RESPUESTAS

```dart
class QuestionService {
  final ApiService _apiService;

  QuestionService(this._apiService);

  // 9.1 OBTENER PREGUNTAS
  Future<Map<String, dynamic>> getQuestions({String? category}) async {
    try {
      String url = '/questions';
      if (category != null) {
        url += '?category=$category';
      }

      final response = await _apiService.get(url);
      return {
        'success': true,
        'questions': response.data['questions'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 9.2 OBTENER PREGUNTA DIARIA
  Future<Map<String, dynamic>> getDailyQuestion() async {
    try {
      final response = await _apiService.get('/daily-question');
      return {
        'success': true,
        'question': response.data['question'],
        'date': response.data['date'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 9.3 RESPONDER PREGUNTA
  Future<Map<String, dynamic>> answerQuestion({
    required String questionId,
    required String answer,
    int? timeSpent,
  }) async {
    try {
      final response = await _apiService.post('/question/answer', data: {
        'questionId': questionId,
        'answer': answer,
        'timeSpent': timeSpent,
      });

      return {
        'success': true,
        'isCorrect': response.data['isCorrect'],
        'correctAnswer': response.data['correctAnswer'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## 🎯 10. PLANES DE VIDA

```dart
class LifePlanService {
  final ApiService _apiService;

  LifePlanService(this._apiService);

  // 10.1 OBTENER PLANES DE VIDA
  Future<Map<String, dynamic>> getLifePlans() async {
    try {
      final response = await _apiService.get('/lifeplans');
      return {
        'success': true,
        'lifePlans': response.data['lifePlans'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 10.2 CREAR PLAN DE VIDA
  Future<Map<String, dynamic>> createLifePlan({
    required String title,
    required String description,
    required String category,
    required String targetDate,
    List<Map<String, dynamic>>? goals,
  }) async {
    try {
      final response = await _apiService.post('/lifeplans', data: {
        'title': title,
        'description': description,
        'category': category,
        'targetDate': targetDate,
        'goals': goals ?? [],
      });

      return {
        'success': true,
        'lifePlan': response.data['lifePlan'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 10.3 ACTUALIZAR PROGRESO
  Future<Map<String, dynamic>> updateLifePlan({
    required String planId,
    String? title,
    String? description,
    String? category,
    String? targetDate,
    int? progress,
    String? status,
  }) async {
    try {
      final response = await _apiService.put('/lifeplan/$planId', data: {
        if (title != null) 'title': title,
        if (description != null) 'description': description,
        if (category != null) 'category': category,
        if (targetDate != null) 'targetDate': targetDate,
        if (progress != null) 'progress': progress,
        if (status != null) 'status': status,
      });

      return {
        'success': true,
        'lifePlan': response.data['lifePlan'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 10.4 AGREGAR META
  Future<Map<String, dynamic>> addGoal({
    required String planId,
    required String title,
    required String description,
    required String targetDate,
    String priority = 'medium',
  }) async {
    try {
      final response = await _apiService.post('/lifeplan/goal', data: {
        'planId': planId,
        'title': title,
        'description': description,
        'targetDate': targetDate,
        'priority': priority,
      });

      return {
        'success': true,
        'goal': response.data['goal'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## 📍 11. LUGARES Y UBICACIONES

```dart
class PlaceService {
  final ApiService _apiService;

  PlaceService(this._apiService);

  // 11.1 OBTENER LUGARES
  Future<Map<String, dynamic>> getPlaces({String? category}) async {
    try {
      String url = '/places';
      if (category != null) {
        url += '?category=$category';
      }

      final response = await _apiService.get(url);
      return {
        'success': true,
        'places': response.data['places'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 11.2 OBTENER LUGARES CERCANOS
  Future<Map<String, dynamic>> getNearbyPlaces({
    required double lat,
    required double lng,
    double radius = 10.0,
  }) async {
    try {
      final response = await _apiService.get('/places/nearby',
        queryParameters: {
          'lat': lat,
          'lng': lng,
          'radius': radius,
        });

      return {
        'success': true,
        'places': response.data['places'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 11.3 MARCAR LUGAR COMO VISITADO
  Future<Map<String, dynamic>> markAsVisited({
    required String placeId,
    String? visitDate,
    String? notes,
  }) async {
    try {
      final response = await _apiService.post('/place/visit', data: {
        'placeId': placeId,
        'visitDate': visitDate ?? DateTime.now().toIso8601String(),
        'notes': notes,
      });

      return {
        'success': true,
        'visit': response.data['visit'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
```

## 💬 12. CHAT EN TIEMPO REAL (WebSocket)

```dart
import 'package:web_socket_channel/web_socket_channel.dart';
import 'dart:convert';

class ChatService {
  WebSocketChannel? _channel;
  final AuthService _authService;
  Function(Map<String, dynamic>)? onMessageReceived;
  Function(Map<String, dynamic>)? onUserJoined;
  Function(Map<String, dynamic>)? onUserLeft;
  Function(Map<String, dynamic>)? onTyping;

  ChatService(this._authService);

  // 12.1 CONECTAR AL WEBSOCKET
  Future<bool> connect() async {
    try {
      final token = _authService.getCurrentToken();
      final userId = _authService.getCurrentUserId(); // Implementar este método

      final uri = Uri.parse('${ApiConfig.WEBSOCKET_URL}?userId=$userId&token=$token');
      _channel = WebSocketChannel.connect(uri);

      // Escuchar mensajes
      _channel!.stream.listen(
        (data) {
          final message = json.decode(data);
          _handleMessage(message);
        },
        onError: (error) {
          print('WebSocket error: $error');
        },
        onDone: () {
          print('WebSocket connection closed');
        },
      );

      return true;
    } catch (e) {
      print('Error connecting to WebSocket: $e');
      return false;
    }
  }

  // 12.2 UNIRSE A UNA SALA
  void joinRoom(String roomId) {
    if (_channel != null) {
      _channel!.sink.add(json.encode({
        'action': 'joinRoom',
        'roomId': roomId,
      }));
    }
  }

  // 12.3 ENVIAR MENSAJE
  void sendMessage({
    required String roomId,
    required String message,
    String messageType = 'text',
  }) {
    if (_channel != null) {
      _channel!.sink.add(json.encode({
        'action': 'sendMessage',
        'roomId': roomId,
        'message': message,
        'messageType': messageType,
      }));
    }
  }

  // 12.4 INDICAR QUE ESTÁ ESCRIBIENDO
  void sendTypingIndicator({
    required String roomId,
    required bool isTyping,
  }) {
    if (_channel != null) {
      _channel!.sink.add(json.encode({
        'action': 'typing',
        'roomId': roomId,
        'isTyping': isTyping,
      }));
    }
  }

  // 12.5 SALIR DE UNA SALA
  void leaveRoom(String roomId) {
    if (_channel != null) {
      _channel!.sink.add(json.encode({
        'action': 'leaveRoom',
        'roomId': roomId,
      }));
    }
  }

  // 12.6 MANEJAR MENSAJES RECIBIDOS
  void _handleMessage(Map<String, dynamic> message) {
    switch (message['type']) {
      case 'message':
        onMessageReceived?.call(message);
        break;
      case 'userJoined':
        onUserJoined?.call(message);
        break;
      case 'userLeft':
        onUserLeft?.call(message);
        break;
      case 'typing':
        onTyping?.call(message);
        break;
      default:
        print('Unknown message type: ${message['type']}');
    }
  }

  // 12.7 DESCONECTAR
  void disconnect() {
    _channel?.sink.close();
    _channel = null;
  }
}

## 📨 13. HISTORIAL DE MENSAJES

```dart
class MessageHistoryService {
  final ApiService _apiService;

  MessageHistoryService(this._apiService);

  // 13.1 OBTENER HISTORIAL DE MENSAJES
  Future<Map<String, dynamic>> getMessageHistory({
    required String roomId,
    int limit = 50,
    String? lastMessageId,
  }) async {
    try {
      Map<String, dynamic> queryParams = {
        'roomId': roomId,
        'limit': limit,
      };

      if (lastMessageId != null) {
        queryParams['lastMessageId'] = lastMessageId;
      }

      final response = await _apiService.get('/messages',
        queryParameters: queryParams);

      return {
        'success': true,
        'messages': response.data['messages'],
        'lastEvaluatedKey': response.data['lastEvaluatedKey'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 13.2 OBTENER SALAS DE CHAT
  Future<Map<String, dynamic>> getRooms() async {
    try {
      final response = await _apiService.get('/rooms');
      return {
        'success': true,
        'rooms': response.data['rooms'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 13.3 CREAR SALA DE CHAT
  Future<Map<String, dynamic>> createRoom({
    required String name,
    required List<String> participants,
    String type = 'group',
  }) async {
    try {
      final response = await _apiService.post('/rooms', data: {
        'name': name,
        'participants': participants,
        'type': type,
      });

      return {
        'success': true,
        'room': response.data['room'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## 🔍 14. BÚSQUEDA

```dart
class SearchService {
  final ApiService _apiService;

  SearchService(this._apiService);

  // 14.1 BÚSQUEDA GENERAL
  Future<Map<String, dynamic>> search({
    required String query,
    String type = 'all', // all, recipes, ingredients
  }) async {
    try {
      final response = await _apiService.get('/search',
        queryParameters: {
          'q': query,
          'type': type,
        });

      return {
        'success': true,
        'results': response.data['results'],
        'count': response.data['count'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## ⭐ 15. FAVORITOS

```dart
class FavoriteService {
  final ApiService _apiService;

  FavoriteService(this._apiService);

  // 15.1 OBTENER FAVORITOS
  Future<Map<String, dynamic>> getFavorites() async {
    try {
      final response = await _apiService.get('/favorites');
      return {
        'success': true,
        'favorites': response.data['favorites'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 15.2 AGREGAR A FAVORITOS
  Future<Map<String, dynamic>> addFavorite({
    required String itemId,
    required String itemType,
    required String title,
  }) async {
    try {
      final response = await _apiService.post('/favorites', data: {
        'itemId': itemId,
        'itemType': itemType,
        'title': title,
      });

      return {
        'success': true,
        'favorite': response.data['favorite'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 15.3 REMOVER DE FAVORITOS
  Future<Map<String, dynamic>> removeFavorite(String favoriteId) async {
    try {
      await _apiService.delete('/favorite/$favoriteId');
      return {'success': true};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
```

## 📊 16. ESTADÍSTICAS

```dart
class StatsService {
  final ApiService _apiService;

  StatsService(this._apiService);

  // 16.1 ESTADÍSTICAS DE USUARIO
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      final response = await _apiService.get('/stats/user');
      return {
        'success': true,
        'userStats': response.data['userStats'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 16.2 ESTADÍSTICAS DE PAREJA
  Future<Map<String, dynamic>> getCoupleStats() async {
    try {
      final response = await _apiService.get('/stats/couple');
      return {
        'success': true,
        'coupleStats': response.data['coupleStats'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 16.3 ESTADÍSTICAS DE ACTIVIDADES
  Future<Map<String, dynamic>> getActivityStats({
    String timeframe = 'week', // week, month, year
  }) async {
    try {
      final response = await _apiService.get('/stats/activities',
        queryParameters: {'timeframe': timeframe});

      return {
        'success': true,
        'activityStats': response.data['activityStats'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 16.4 RASTREAR ACTIVIDAD
  Future<Map<String, dynamic>> trackActivity({
    required String activityType,
    required dynamic value,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _apiService.post('/stats/track', data: {
        'activityType': activityType,
        'value': value,
        'metadata': metadata,
      });

      return {
        'success': true,
        'activity': response.data['activity'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## ⏰ 17. PROGRAMADOR DE TAREAS

```dart
class SchedulerService {
  final ApiService _apiService;

  SchedulerService(this._apiService);

  // 17.1 OBTENER PROGRAMACIONES
  Future<Map<String, dynamic>> getSchedules({String? type}) async {
    try {
      String url = '/schedules';
      if (type != null) {
        url += '?type=$type';
      }

      final response = await _apiService.get(url);
      return {
        'success': true,
        'schedules': response.data['schedules'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 17.2 CREAR PROGRAMACIÓN
  Future<Map<String, dynamic>> createSchedule({
    required String title,
    required String description,
    required String type,
    required String scheduledTime,
    String recurrence = 'none',
    required Map<String, dynamic> action,
    bool isActive = true,
  }) async {
    try {
      final response = await _apiService.post('/schedules', data: {
        'title': title,
        'description': description,
        'type': type,
        'scheduledTime': scheduledTime,
        'recurrence': recurrence,
        'action': action,
        'isActive': isActive,
      });

      return {
        'success': true,
        'schedule': response.data['schedule'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // 17.3 OBTENER PRÓXIMAS TAREAS
  Future<Map<String, dynamic>> getUpcomingSchedules({int hours = 24}) async {
    try {
      final response = await _apiService.get('/schedule/upcoming',
        queryParameters: {'hours': hours});

      return {
        'success': true,
        'upcomingSchedules': response.data['upcomingSchedules'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}

## 🏗️ 18. CONFIGURACIÓN DE SERVICIOS (Dependency Injection)

```dart
import 'package:provider/provider.dart';

class ServiceLocator {
  static late AuthService _authService;
  static late ApiService _apiService;

  // Servicios
  static late UserService userService;
  static late RecipeService recipeService;
  static late IngredientService ingredientService;
  static late ShoppingListService shoppingListService;
  static late TreeService treeService;
  static late CoupleService coupleService;
  static late QuestionService questionService;
  static late LifePlanService lifePlanService;
  static late PlaceService placeService;
  static late ChatService chatService;
  static late MessageHistoryService messageHistoryService;
  static late SearchService searchService;
  static late FavoriteService favoriteService;
  static late StatsService statsService;
  static late SchedulerService schedulerService;

  static void initialize() {
    _authService = AuthService();
    _apiService = ApiService(_authService);

    // Inicializar todos los servicios
    userService = UserService(_apiService);
    recipeService = RecipeService(_apiService);
    ingredientService = IngredientService(_apiService);
    shoppingListService = ShoppingListService(_apiService);
    treeService = TreeService(_apiService);
    coupleService = CoupleService(_apiService);
    questionService = QuestionService(_apiService);
    lifePlanService = LifePlanService(_apiService);
    placeService = PlaceService(_apiService);
    chatService = ChatService(_authService);
    messageHistoryService = MessageHistoryService(_apiService);
    searchService = SearchService(_apiService);
    favoriteService = FavoriteService(_apiService);
    statsService = StatsService(_apiService);
    schedulerService = SchedulerService(_apiService);
  }

  static AuthService get authService => _authService;
}

## 📱 19. CONFIGURACIÓN EN MAIN.DART

```dart
void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Inicializar servicios
  ServiceLocator.initialize();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => RecipeProvider()),
        ChangeNotifierProvider(create: (_) => ChatProvider()),
        // Agregar más providers según necesites
      ],
      child: MaterialApp(
        title: 'HopieApp',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: AuthWrapper(),
      ),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.isAuthenticated) {
          return HomeScreen();
        } else {
          return LoginScreen();
        }
      },
    );
  }
}
```

## 💡 20. EJEMPLOS DE USO

### 20.1 Ejemplo de Login:
```dart
class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;

  Future<void> _login() async {
    setState(() => _isLoading = true);

    final result = await ServiceLocator.authService.signIn(
      email: _emailController.text,
      password: _passwordController.text,
    );

    setState(() => _isLoading = false);

    if (result['success']) {
      // Navegar a la pantalla principal
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => HomeScreen()),
      );
    } else {
      // Mostrar error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result['error'])),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextField(
              controller: _emailController,
              decoration: InputDecoration(labelText: 'Email'),
            ),
            TextField(
              controller: _passwordController,
              decoration: InputDecoration(labelText: 'Password'),
              obscureText: true,
            ),
            SizedBox(height: 20),
            _isLoading
                ? CircularProgressIndicator()
                : ElevatedButton(
                    onPressed: _login,
                    child: Text('Iniciar Sesión'),
                  ),
          ],
        ),
      ),
    );
  }
}
```

### 20.2 Ejemplo de Chat:
```dart
class ChatScreen extends StatefulWidget {
  final String roomId;

  ChatScreen({required this.roomId});

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final _messageController = TextEditingController();
  final List<Map<String, dynamic>> _messages = [];
  late ChatService _chatService;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    _chatService = ServiceLocator.chatService;

    // Configurar callbacks
    _chatService.onMessageReceived = (message) {
      setState(() {
        _messages.add(message);
      });
    };

    // Conectar y unirse a la sala
    await _chatService.connect();
    _chatService.joinRoom(widget.roomId);

    // Cargar historial de mensajes
    await _loadMessageHistory();
  }

  Future<void> _loadMessageHistory() async {
    final result = await ServiceLocator.messageHistoryService
        .getMessageHistory(roomId: widget.roomId);

    if (result['success']) {
      setState(() {
        _messages.addAll(List<Map<String, dynamic>>.from(
          result['messages'].reversed));
      });
    }
  }

  void _sendMessage() {
    if (_messageController.text.isNotEmpty) {
      _chatService.sendMessage(
        roomId: widget.roomId,
        message: _messageController.text,
      );
      _messageController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Chat')),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return ListTile(
                  title: Text(message['message']),
                  subtitle: Text(message['userId']),
                );
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Escribe un mensaje...',
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send),
                  onPressed: _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _chatService.disconnect();
    super.dispose();
  }
}
```

### 20.3 Ejemplo de Gestión de Recetas:
```dart
class RecipeListScreen extends StatefulWidget {
  @override
  _RecipeListScreenState createState() => _RecipeListScreenState();
}

class _RecipeListScreenState extends State<RecipeListScreen> {
  List<Map<String, dynamic>> _recipes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecipes();
  }

  Future<void> _loadRecipes() async {
    final result = await ServiceLocator.recipeService.getRecipes();

    setState(() {
      _isLoading = false;
      if (result['success']) {
        _recipes = List<Map<String, dynamic>>.from(result['recipes']);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Recetas'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => CreateRecipeScreen()),
              ).then((_) => _loadRecipes());
            },
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: _recipes.length,
              itemBuilder: (context, index) {
                final recipe = _recipes[index];
                return Card(
                  child: ListTile(
                    title: Text(recipe['title']),
                    subtitle: Text(recipe['description']),
                    trailing: Text('${recipe['cookingTime']} min'),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => RecipeDetailScreen(
                            recipeId: recipe['id'],
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
    );
  }
}
```

## 🔧 21. CONFIGURACIONES ADICIONALES

### 21.1 Permisos en Android (android/app/src/main/AndroidManifest.xml):
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

### 21.2 Configuración iOS (ios/Runner/Info.plist):
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>Esta app necesita acceso a la ubicación para mostrar lugares cercanos</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>Esta app necesita acceso a la ubicación para funciones de ubicación</string>
```

## 📋 22. RESUMEN DE ENDPOINTS

### Autenticación:
- **Cognito**: Registro, login, confirmación
- **Token**: Incluir en header `Authorization: Bearer {token}`

### APIs REST (Base: https://ovxbfj0noa.execute-api.us-east-2.amazonaws.com/dev):
- `/user/*` - Gestión de usuarios
- `/recipes` - Gestión de recetas
- `/ingredients` - Gestión de ingredientes
- `/shopping-lists` - Listas de compras
- `/trees` - Gestión de árboles
- `/couples` - Gestión de parejas
- `/questions` - Preguntas y respuestas
- `/lifeplans` - Planes de vida
- `/places` - Lugares y ubicaciones
- `/search` - Búsqueda
- `/favorites` - Favoritos
- `/stats/*` - Estadísticas
- `/schedules` - Programador de tareas
- `/messages` - Historial de mensajes
- `/rooms` - Salas de chat

### WebSocket (wss://w7xk45by82.execute-api.us-east-2.amazonaws.com/dev/):
- **Acciones**: `joinRoom`, `sendMessage`, `typing`, `leaveRoom`
- **Eventos**: `message`, `userJoined`, `userLeft`, `typing`

## 🚀 23. PASOS PARA IMPLEMENTAR

1. **Configurar dependencias** en `pubspec.yaml`
2. **Crear estructura de servicios** con los códigos proporcionados
3. **Configurar autenticación** con Cognito
4. **Implementar servicios REST** uno por uno
5. **Agregar WebSocket** para chat en tiempo real
6. **Crear providers** para gestión de estado
7. **Implementar pantallas** usando los servicios
8. **Probar funcionalidades** con datos de prueba
9. **Configurar permisos** para Android/iOS
10. **Optimizar y pulir** la experiencia de usuario

¡Con esta guía tienes todo lo necesario para implementar HopieApp en Flutter! 🎉
```
