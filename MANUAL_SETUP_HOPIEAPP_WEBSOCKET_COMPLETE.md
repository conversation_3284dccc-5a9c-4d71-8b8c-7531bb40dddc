# 🚀 HopieApp - WebSocket API y Configuración Completa

## 6. API Gateway WebSocket - CONFIGURACIÓN COMPLETA PASO A PASO

### 6.1 Crear API Gateway WebSocket
1. Ve a **AWS Console** → Busca **API Gateway** → Click **API Gateway**
2. En la página principal, click **Create API**
3. **WebSocket API** → Click **Build**
4. **Create and configure integrations**:
   - **API name**: `hopie-websocket-dev`
   - **Route selection expression**: `$request.body.action`
   - **Description**: `HopieApp WebSocket API for real-time chat`
5. Click **Next**

### 6.2 Configurar Rutas
**En la pantalla "Configure routes":**

**Ruta 1: $connect**
1. **Route key**: `$connect` (ya está)
2. **Integration type**: **Lambda**
3. **Lambda function**: `hopie-chat-dev`
4. **Integration description**: `Handle WebSocket connections`

**Ruta 2: $disconnect**
1. Click **Add route**
2. **Route key**: `$disconnect`
3. **Integration type**: **Lambda**
4. **Lambda function**: `hopie-chat-dev`
5. **Integration description**: `Handle WebSocket disconnections`

**Ruta 3: sendMessage**
1. Click **Add route**
2. **Route key**: `sendMessage`
3. **Integration type**: **Lambda**
4. **Lambda function**: `hopie-chat-dev`
5. **Integration description**: `Handle sending messages`

**Ruta 4: joinRoom**
1. Click **Add route**
2. **Route key**: `joinRoom`
3. **Integration type**: **Lambda**
4. **Lambda function**: `hopie-chat-dev`
5. **Integration description**: `Handle joining chat rooms`

6. Click **Next**

### 6.3 Configurar Stage
1. **Stage name**: `dev`
2. **Stage description**: `Development stage for WebSocket`
3. **Auto deploy**: ✅ (SÍ marcar)
4. Click **Next**

### 6.4 Review y Create
1. Revisar toda la configuración
2. Click **Create and deploy**

### 6.5 Obtener WebSocket URL
1. Ve a **Stages** → **dev**
2. **WebSocket URL**: `wss://XXXXXXXXXX.execute-api.us-east-2.amazonaws.com/dev`
3. **ANOTA ESTA URL** - la necesitarás para Flutter

---

## 7. TODAS LAS FUNCIONES LAMBDA - CONFIGURACIÓN COMPLETA

### 7.1 Función 3: CoupleFunction
1. **Lambda** → **Create function**
2. **Author from scratch**
3. **Function name**: `hopie-couple-dev`
4. **Runtime**: **Node.js 18.x**
5. **Architecture**: **x86_64**
6. **Execution role**: **Use an existing role** → `hopie-lambda-execution-role`
7. Click **Create function**

**Código para CoupleFunction:**
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'POST' && path.includes('/create')) {
            return await createCouple(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/info')) {
            return await getCoupleInfo(event);
        } else if (httpMethod === 'PUT' && path.includes('/update')) {
            return await updateCouple(requestBody, event);
        } else if (httpMethod === 'POST' && path.includes('/invite')) {
            return await invitePartner(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function createCouple(body, event) {
    const userId = 'user-123'; // Extraer del token
    const { partnerEmail, relationshipStart } = body;
    const coupleId = `COUPLE#${Date.now()}`;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: coupleId,
            SK: 'INFO',
            user1: userId,
            user2: null,
            partnerEmail,
            relationshipStart,
            status: 'pending',
            createdAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple created successfully',
            coupleId,
            status: 'pending'
        })
    };
}

async function getCoupleInfo(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :pk',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            couple: result.Items[0] || null
        })
    };
}

async function updateCouple(body, event) {
    const { coupleId, relationshipStart, anniversary } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: coupleId,
            SK: 'INFO'
        },
        UpdateExpression: 'SET relationshipStart = :start, anniversary = :anniversary, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':start': relationshipStart,
            ':anniversary': anniversary,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple updated successfully',
            couple: result.Attributes
        })
    };
}

async function invitePartner(body, event) {
    const { email } = body;
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Invitation sent successfully',
            email
        })
    };
}
```

**Variables de entorno:**
- `TABLE_NAME`: `hopie-main-table-dev`

**Configuración:**
- **Timeout**: 30 seconds
- **Memory**: 256 MB

### 7.2 Función 4: TreeFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-tree-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART2.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.3 Función 5: QuestionsFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-questions-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART2.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.4 Función 6: LifePlanFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-lifeplan-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART2.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.5 Función 7: PlacesFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-places-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART2.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.6 Función 8: LocationFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-location-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART2.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.7 Función 9: StatsFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-stats-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART2.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.8 Función 10: ImageFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-image-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART3.md]
- **Variables**: 
  - `TABLE_NAME` = `hopie-main-table-dev`
  - `BUCKET_NAME` = `hopie-app-assets-dev-123456`

### 7.9 Función 11: NotificationFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-notification-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART3.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.10 Función 12: ChatFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-chat-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART3.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`

### 7.11 Función 13: ImageProcessorFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-image-processor-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART3.md]
- **Variables**: 
  - `TABLE_NAME` = `hopie-main-table-dev`
  - `BUCKET_NAME` = `hopie-app-assets-dev-123456`

### 7.12 Función 14: SchedulerFunction
**Repite el mismo proceso con:**
- **Function name**: `hopie-scheduler-dev`
- **Código**: [Ver en MANUAL_SETUP_HOPIEAPP_PART3.md]
- **Variables**: `TABLE_NAME` = `hopie-main-table-dev`
