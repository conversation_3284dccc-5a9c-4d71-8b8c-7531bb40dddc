#!/bin/bash

# 🚀 Script para crear las funciones Lambda de WebSocket y Chat de HopieApp
# Funciones específicas para la API de WebSocket

TABLE_NAME="hopie-main-table-dev"
USER_POOL_ID="us-east-2_gTZjXep7j"
CLIENT_ID="63604alcg1rvili3ksfan61fid"
REGION="us-east-2"
BUCKET_NAME="hopie-app-assets-dev-123456"
ROLE_ARN="arn:aws:iam::864899858226:role/hopie-lambda-execution-role"

echo "🚀 Creando las funciones Lambda de WebSocket y Chat para HopieApp..."
echo "📋 Variables configuradas:"
echo "   TABLE_NAME: $TABLE_NAME"
echo "   USER_POOL_ID: $USER_POOL_ID"
echo "   CLIENT_ID: $CLIENT_ID"
echo "   REGION: $REGION"
echo "   BUCKET_NAME: $BUCKET_NAME"
echo "   ROLE_ARN: $ROLE_ARN"
echo ""

# Función para crear Lambda
create_lambda() {
    local FUNCTION_NAME=$1
    local DESCRIPTION=$2
    local ENV_VARS=$3
    
    echo "📦 Creando función: $FUNCTION_NAME"
    
    # Crear archivo ZIP temporal
    cd lambda-functions/$FUNCTION_NAME
    zip -r ../../${FUNCTION_NAME}.zip . > /dev/null 2>&1
    cd ../..
    
    # Crear función Lambda
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://${FUNCTION_NAME}.zip \
        --description "$DESCRIPTION" \
        --timeout 30 \
        --memory-size 256 \
        --environment Variables="$ENV_VARS" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $FUNCTION_NAME creada exitosamente"
    else
        echo "   ❌ Error creando $FUNCTION_NAME"
    fi
    
    # Limpiar archivo ZIP
    rm ${FUNCTION_NAME}.zip
}

# Crear directorio para funciones
mkdir -p lambda-functions

# 1. ChatFunction - Función principal de chat con WebSocket
mkdir -p lambda-functions/hopie-chat-dev
cat > lambda-functions/hopie-chat-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const apigatewaymanagementapi = new AWS.ApiGatewayManagementApi({
    endpoint: process.env.WEBSOCKET_ENDPOINT
});

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { requestContext, body } = event;
    const { routeKey, connectionId } = requestContext;
    
    try {
        switch (routeKey) {
            case '$connect':
                return await handleConnect(connectionId, event);
            case '$disconnect':
                return await handleDisconnect(connectionId, event);
            case 'sendMessage':
                return await handleSendMessage(connectionId, JSON.parse(body || '{}'), event);
            case 'joinRoom':
                return await handleJoinRoom(connectionId, JSON.parse(body || '{}'), event);
            case 'leaveRoom':
                return await handleLeaveRoom(connectionId, JSON.parse(body || '{}'), event);
            case 'typing':
                return await handleTyping(connectionId, JSON.parse(body || '{}'), event);
            default:
                return { statusCode: 400, body: 'Unknown route' };
        }
    } catch (error) {
        console.error('Error:', error);
        return { statusCode: 500, body: 'Internal server error' };
    }
};

async function handleConnect(connectionId, event) {
    const userId = event.queryStringParameters?.userId || 'anonymous';
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA',
            connectionId,
            userId,
            connectedAt: new Date().toISOString(),
            status: 'connected'
        }
    };
    
    await dynamodb.put(params).promise();
    
    console.log(`User ${userId} connected with connection ${connectionId}`);
    return { statusCode: 200 };
}

async function handleDisconnect(connectionId, event) {
    // Remove connection from database
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    await dynamodb.delete(params).promise();
    
    console.log(`Connection ${connectionId} disconnected`);
    return { statusCode: 200 };
}

async function handleSendMessage(connectionId, data, event) {
    const { roomId, message, messageType } = data;
    const messageId = `msg-${Date.now()}`;
    
    // Get connection info to get userId
    const connectionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    const connectionResult = await dynamodb.get(connectionParams).promise();
    const userId = connectionResult.Item?.userId || 'anonymous';
    
    // Save message to database
    const messageParams = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ROOM#${roomId}`,
            SK: `MESSAGE#${messageId}`,
            messageId,
            userId,
            message,
            messageType: messageType || 'text',
            timestamp: new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(messageParams).promise();
    
    // Get all connections in the room
    const roomConnectionsParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ROOM#${roomId}`,
            ':sk': 'CONNECTION#'
        }
    };
    
    const roomConnections = await dynamodb.query(roomConnectionsParams).promise();
    
    // Send message to all connections in the room
    const messageData = {
        type: 'message',
        messageId,
        roomId,
        userId,
        message,
        messageType,
        timestamp: new Date().toISOString()
    };
    
    const sendPromises = (roomConnections.Items || []).map(async (connection) => {
        try {
            await apigatewaymanagementapi.postToConnection({
                ConnectionId: connection.connectionId,
                Data: JSON.stringify(messageData)
            }).promise();
        } catch (error) {
            if (error.statusCode === 410) {
                // Connection is stale, remove it
                await dynamodb.delete({
                    TableName: TABLE_NAME,
                    Key: {
                        PK: `ROOM#${roomId}`,
                        SK: `CONNECTION#${connection.connectionId}`
                    }
                }).promise();
            }
        }
    });
    
    await Promise.all(sendPromises);
    
    return { statusCode: 200 };
}

async function handleJoinRoom(connectionId, data, event) {
    const { roomId } = data;
    
    // Get connection info
    const connectionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    const connectionResult = await dynamodb.get(connectionParams).promise();
    const userId = connectionResult.Item?.userId || 'anonymous';
    
    // Add connection to room
    const roomConnectionParams = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ROOM#${roomId}`,
            SK: `CONNECTION#${connectionId}`,
            connectionId,
            userId,
            joinedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(roomConnectionParams).promise();
    
    // Notify other users in the room
    const notificationData = {
        type: 'userJoined',
        roomId,
        userId,
        timestamp: new Date().toISOString()
    };
    
    await sendToConnection(connectionId, JSON.stringify({
        type: 'joinedRoom',
        roomId,
        message: 'Successfully joined room'
    }));
    
    console.log(`User ${userId} joined room ${roomId}`);
    return { statusCode: 200 };
}

async function handleLeaveRoom(connectionId, data, event) {
    const { roomId } = data;
    
    // Remove connection from room
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `ROOM#${roomId}`,
            SK: `CONNECTION#${connectionId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    console.log(`Connection ${connectionId} left room ${roomId}`);
    return { statusCode: 200 };
}

async function handleTyping(connectionId, data, event) {
    const { roomId, isTyping } = data;
    
    // Get connection info
    const connectionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    const connectionResult = await dynamodb.get(connectionParams).promise();
    const userId = connectionResult.Item?.userId || 'anonymous';
    
    // Get all other connections in the room
    const roomConnectionsParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        FilterExpression: 'connectionId <> :currentConnection',
        ExpressionAttributeValues: {
            ':pk': `ROOM#${roomId}`,
            ':sk': 'CONNECTION#',
            ':currentConnection': connectionId
        }
    };
    
    const roomConnections = await dynamodb.query(roomConnectionsParams).promise();
    
    // Send typing indicator to other users
    const typingData = {
        type: 'typing',
        roomId,
        userId,
        isTyping,
        timestamp: new Date().toISOString()
    };
    
    const sendPromises = (roomConnections.Items || []).map(async (connection) => {
        try {
            await apigatewaymanagementapi.postToConnection({
                ConnectionId: connection.connectionId,
                Data: JSON.stringify(typingData)
            }).promise();
        } catch (error) {
            if (error.statusCode === 410) {
                // Connection is stale, remove it
                await dynamodb.delete({
                    TableName: TABLE_NAME,
                    Key: {
                        PK: `ROOM#${roomId}`,
                        SK: `CONNECTION#${connection.connectionId}`
                    }
                }).promise();
            }
        }
    });
    
    await Promise.all(sendPromises);
    
    return { statusCode: 200 };
}

async function sendToConnection(connectionId, data) {
    try {
        await apigatewaymanagementapi.postToConnection({
            ConnectionId: connectionId,
            Data: data
        }).promise();
    } catch (error) {
        console.error('Error sending to connection:', error);
    }
}
EOF

# 2. WebSocket Connect Function
mkdir -p lambda-functions/hopie-websocket-connect-dev
cat > lambda-functions/hopie-websocket-connect-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('WebSocket Connect Event:', JSON.stringify(event, null, 2));

    const { requestContext } = event;
    const { connectionId } = requestContext;
    const userId = event.queryStringParameters?.userId || 'anonymous';

    try {
        const params = {
            TableName: TABLE_NAME,
            Item: {
                PK: `CONNECTION#${connectionId}`,
                SK: 'METADATA',
                connectionId,
                userId,
                connectedAt: new Date().toISOString(),
                status: 'connected',
                lastActivity: new Date().toISOString()
            }
        };

        await dynamodb.put(params).promise();

        console.log(`User ${userId} connected with connection ${connectionId}`);

        return {
            statusCode: 200,
            body: 'Connected'
        };
    } catch (error) {
        console.error('Error in connect:', error);
        return {
            statusCode: 500,
            body: 'Failed to connect'
        };
    }
};
EOF

# 3. WebSocket Disconnect Function
mkdir -p lambda-functions/hopie-websocket-disconnect-dev
cat > lambda-functions/hopie-websocket-disconnect-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('WebSocket Disconnect Event:', JSON.stringify(event, null, 2));

    const { requestContext } = event;
    const { connectionId } = requestContext;

    try {
        // Remove connection from main connections table
        const connectionParams = {
            TableName: TABLE_NAME,
            Key: {
                PK: `CONNECTION#${connectionId}`,
                SK: 'METADATA'
            }
        };

        await dynamodb.delete(connectionParams).promise();

        // Remove connection from all rooms
        const roomsParams = {
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk AND GSI1SK = :gsi1sk',
            ExpressionAttributeValues: {
                ':gsi1pk': 'CONNECTION',
                ':gsi1sk': connectionId
            }
        };

        const roomsResult = await dynamodb.query(roomsParams).promise();

        // Remove from each room
        const deletePromises = (roomsResult.Items || []).map(item => {
            return dynamodb.delete({
                TableName: TABLE_NAME,
                Key: {
                    PK: item.PK,
                    SK: item.SK
                }
            }).promise();
        });

        await Promise.all(deletePromises);

        console.log(`Connection ${connectionId} disconnected and cleaned up`);

        return {
            statusCode: 200,
            body: 'Disconnected'
        };
    } catch (error) {
        console.error('Error in disconnect:', error);
        return {
            statusCode: 500,
            body: 'Failed to disconnect'
        };
    }
};
EOF

# 4. Message History Function
mkdir -p lambda-functions/hopie-message-history-dev
cat > lambda-functions/hopie-message-history-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Message History Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, queryStringParameters } = event;

    try {
        if (httpMethod === 'GET' && path.includes('/messages')) {
            return await getMessageHistory(event);
        } else if (httpMethod === 'GET' && path.includes('/rooms')) {
            return await getRooms(event);
        } else if (httpMethod === 'POST' && path.includes('/rooms')) {
            return await createRoom(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getMessageHistory(event) {
    const roomId = event.queryStringParameters?.roomId;
    const limit = parseInt(event.queryStringParameters?.limit || '50');
    const lastMessageId = event.queryStringParameters?.lastMessageId;

    if (!roomId) {
        return {
            statusCode: 400,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'roomId is required' })
        };
    }

    let params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ROOM#${roomId}`,
            ':sk': 'MESSAGE#'
        },
        ScanIndexForward: false,
        Limit: limit
    };

    if (lastMessageId) {
        params.ExclusiveStartKey = {
            PK: `ROOM#${roomId}`,
            SK: `MESSAGE#${lastMessageId}`
        };
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            messages: result.Items || [],
            lastEvaluatedKey: result.LastEvaluatedKey
        })
    };
}

async function getRooms(event) {
    const userId = event.queryStringParameters?.userId || 'user-123';

    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        FilterExpression: 'contains(participants, :userId)',
        ExpressionAttributeValues: {
            ':gsi1pk': 'ROOM',
            ':userId': userId
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            rooms: result.Items || []
        })
    };
}

async function createRoom(event) {
    const body = JSON.parse(event.body || '{}');
    const { name, participants, type } = body;
    const roomId = `room-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ROOM#${roomId}`,
            SK: 'METADATA',
            GSI1PK: 'ROOM',
            GSI1SK: roomId,
            roomId,
            name,
            participants: participants || [],
            type: type || 'group',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Room created successfully',
            room: params.Item
        })
    };
}
EOF

echo "📁 Creando las funciones Lambda de WebSocket y Chat..."
echo ""

# Crear las funciones Lambda
echo "🔄 Creando hopie-chat-dev..."
create_lambda "hopie-chat-dev" "HopieApp WebSocket Chat Function" "{TABLE_NAME=$TABLE_NAME,WEBSOCKET_ENDPOINT=wss://your-websocket-api-id.execute-api.us-east-2.amazonaws.com/dev}"

echo "🔄 Creando hopie-websocket-connect-dev..."
create_lambda "hopie-websocket-connect-dev" "HopieApp WebSocket Connect Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-websocket-disconnect-dev..."
create_lambda "hopie-websocket-disconnect-dev" "HopieApp WebSocket Disconnect Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-message-history-dev..."
create_lambda "hopie-message-history-dev" "HopieApp Message History Function" "{TABLE_NAME=$TABLE_NAME}"

echo ""
echo "🎉 ¡Todas las funciones Lambda de WebSocket y Chat han sido creadas!"
echo ""
echo "📋 Funciones creadas:"
echo "   ✅ hopie-chat-dev (Función principal de WebSocket)"
echo "   ✅ hopie-websocket-connect-dev (Conexión WebSocket)"
echo "   ✅ hopie-websocket-disconnect-dev (Desconexión WebSocket)"
echo "   ✅ hopie-message-history-dev (Historial de mensajes)"
echo ""
echo "🔗 Para verificar, ejecuta:"
echo "   aws lambda list-functions --query 'Functions[?starts_with(FunctionName, \`hopie-\`)].FunctionName' --output table"
echo ""
echo "⚠️  IMPORTANTE: Recuerda configurar el WebSocket API Gateway y actualizar la variable WEBSOCKET_ENDPOINT"
echo "🚀 ¡Las funciones de chat en tiempo real están listas!"
