const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Message History Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, queryStringParameters } = event;

    try {
        if (httpMethod === 'GET' && path.includes('/messages')) {
            return await getMessageHistory(event);
        } else if (httpMethod === 'GET' && path.includes('/rooms')) {
            return await getRooms(event);
        } else if (httpMethod === 'POST' && path.includes('/rooms')) {
            return await createRoom(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getMessageHistory(event) {
    const roomId = event.queryStringParameters?.roomId;
    const limit = parseInt(event.queryStringParameters?.limit || '50');
    const lastMessageId = event.queryStringParameters?.lastMessageId;

    if (!roomId) {
        return {
            statusCode: 400,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'roomId is required' })
        };
    }

    let params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ROOM#${roomId}`,
            ':sk': 'MESSAGE#'
        },
        ScanIndexForward: false,
        Limit: limit
    };

    if (lastMessageId) {
        params.ExclusiveStartKey = {
            PK: `ROOM#${roomId}`,
            SK: `MESSAGE#${lastMessageId}`
        };
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            messages: result.Items || [],
            lastEvaluatedKey: result.LastEvaluatedKey
        })
    };
}

async function getRooms(event) {
    const userId = event.queryStringParameters?.userId || 'user-123';

    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        FilterExpression: 'contains(participants, :userId)',
        ExpressionAttributeValues: {
            ':gsi1pk': 'ROOM',
            ':userId': userId
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            rooms: result.Items || []
        })
    };
}

async function createRoom(event) {
    const body = JSON.parse(event.body || '{}');
    const { name, participants, type } = body;
    const roomId = `room-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ROOM#${roomId}`,
            SK: 'METADATA',
            GSI1PK: 'ROOM',
            GSI1SK: roomId,
            roomId,
            name,
            participants: participants || [],
            type: type || 'group',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Room created successfully',
            room: params.Item
        })
    };
}
