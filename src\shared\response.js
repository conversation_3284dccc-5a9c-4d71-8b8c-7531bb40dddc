/**
 * Standard HTTP response utilities for HopieApp
 */

const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
  'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
  'Content-Type': 'application/json'
};

/**
 * Create a successful response
 */
function success(data, statusCode = 200) {
  return {
    statusCode,
    headers: CORS_HEADERS,
    body: JSON.stringify({
      success: true,
      data,
      timestamp: new Date().toISOString()
    })
  };
}

/**
 * Create an error response
 */
function error(message, statusCode = 500, errorCode = null) {
  console.error('API Error:', { message, statusCode, errorCode });

  return {
    statusCode,
    headers: CORS_HEADERS,
    body: JSON.stringify({
      success: false,
      error: {
        message,
        code: errorCode,
        timestamp: new Date().toISOString()
      }
    })
  };
}

/**
 * Create a validation error response
 */
function validationError(errors) {
  return {
    statusCode: 400,
    headers: CORS_HEADERS,
    body: JSON.stringify({
      success: false,
      error: {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: errors,
        timestamp: new Date().toISOString()
      }
    })
  };
}

/**
 * Create an unauthorized response
 */
function unauthorized(message = 'Unauthorized') {
  return error(message, 401, 'UNAUTHORIZED');
}

/**
 * Create a forbidden response
 */
function forbidden(message = 'Forbidden') {
  return error(message, 403, 'FORBIDDEN');
}

/**
 * Create a not found response
 */
function notFound(message = 'Resource not found') {
  return error(message, 404, 'NOT_FOUND');
}

/**
 * Create a conflict response
 */
function conflict(message = 'Resource already exists') {
  return error(message, 409, 'CONFLICT');
}

/**
 * Create a too many requests response
 */
function tooManyRequests(message = 'Too many requests') {
  return error(message, 429, 'TOO_MANY_REQUESTS');
}

/**
 * Handle CORS preflight requests
 */
function corsResponse() {
  return {
    statusCode: 200,
    headers: CORS_HEADERS,
    body: ''
  };
}

/**
 * Create a paginated response
 */
function paginated(data, pagination = {}) {
  return success({
    items: data,
    pagination: {
      total: pagination.total || data.length,
      page: pagination.page || 1,
      limit: pagination.limit || 20,
      hasNext: pagination.hasNext || false,
      hasPrev: pagination.hasPrev || false,
      nextToken: pagination.nextToken || null
    }
  });
}

/**
 * Create a created response (201)
 */
function created(data) {
  return success(data, 201);
}

/**
 * Create a no content response (204)
 */
function noContent() {
  return {
    statusCode: 204,
    headers: CORS_HEADERS,
    body: ''
  };
}

/**
 * Handle async errors and return appropriate response
 */
function handleError(err) {
  console.error('Unhandled error:', err);

  // DynamoDB errors
  if (err.code === 'ConditionalCheckFailedException') {
    return conflict('Resource already exists or condition not met');
  }

  if (err.code === 'ResourceNotFoundException') {
    return notFound('Resource not found');
  }

  if (err.code === 'ValidationException') {
    return validationError([{ message: err.message }]);
  }

  if (err.code === 'ProvisionedThroughputExceededException') {
    return tooManyRequests('Service temporarily unavailable');
  }

  // Cognito errors
  if (err.code === 'UserNotFoundException') {
    return notFound('User not found');
  }

  if (err.code === 'NotAuthorizedException') {
    return unauthorized('Invalid credentials');
  }

  if (err.code === 'UsernameExistsException') {
    return conflict('User already exists');
  }

  // S3 errors
  if (err.code === 'NoSuchKey') {
    return notFound('File not found');
  }

  if (err.code === 'AccessDenied') {
    return forbidden('Access denied');
  }

  // Generic errors
  if (err.statusCode) {
    return error(err.message || 'An error occurred', err.statusCode);
  }

  // Default server error
  return error('Internal server error', 500, 'INTERNAL_ERROR');
}

module.exports = {
  success,
  error,
  validationError,
  unauthorized,
  forbidden,
  notFound,
  conflict,
  tooManyRequests,
  corsResponse,
  paginated,
  created,
  noContent,
  handleError,
  CORS_HEADERS
};
