#!/bin/bash

# 🚀 Script para crear pareja real en HopieApp usando los endpoints
# Crea usuarios en Cognito y establece relación de pareja con datos reales

API_BASE="https://ovxbfj0noa.execute-api.us-east-2.amazonaws.com/dev"
REGION="us-east-2"

# Datos de la pareja
RICARDO_EMAIL="<EMAIL>"
RICARDO_PASSWORD="\$Adios123456"
RICARDO_NAME="<PERSON> Arre<PERSON>"
RICARDO_PHONE="+1234567890"

DAMARIS_EMAIL="<EMAIL>"
DAMARIS_PASSWORD="\$Adios123456"
DAMARIS_NAME="Damaris G Coronado"
DAMARIS_PHONE="+1234567891"

echo "💕 Creando pareja real en HopieApp..."
echo "👨 Ricardo: $RICARDO_EMAIL"
echo "👩 Damaris: $DAMARIS_EMAIL"
echo "🌐 API Base: $API_BASE"
echo ""

# Función para hacer peticiones HTTP
make_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="$4"
    
    echo "🔄 $description"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE$endpoint")
    elif [ "$method" = "GET" ]; then
        response=$(curl -s -X GET \
            -H "Content-Type: application/json" \
            "$API_BASE$endpoint")
    fi
    
    echo "   📤 Request: $method $endpoint"
    echo "   📥 Response: $response"
    echo ""
    
    # Verificar si la respuesta contiene error
    if echo "$response" | grep -q '"error"'; then
        echo "   ❌ Error en $description"
        return 1
    else
        echo "   ✅ $description exitoso"
        return 0
    fi
}

echo "👨 === CREANDO USUARIO RICARDO ==="
echo ""

# 1. Registrar Ricardo
make_request "POST" "/auth/register" '{
    "email": "'$RICARDO_EMAIL'",
    "password": "'$RICARDO_PASSWORD'",
    "name": "'$RICARDO_NAME'",
    "phone": "'$RICARDO_PHONE'"
}' "Registrando Ricardo en Cognito"

# 2. Crear perfil de Ricardo
make_request "POST" "/user/profile" '{
    "email": "'$RICARDO_EMAIL'",
    "name": "'$RICARDO_NAME'",
    "phone": "'$RICARDO_PHONE'",
    "birthDate": "1990-05-15",
    "bio": "Desarrollador apasionado por la tecnología y las relaciones auténticas",
    "interests": ["tecnología", "viajes", "música", "deportes"]
}' "Creando perfil de Ricardo"

# 3. Crear preferencias de Ricardo
make_request "POST" "/user/preferences" '{
    "notifications": true,
    "theme": "light",
    "language": "es",
    "privacy": {
        "showLocation": true,
        "showOnlineStatus": true
    }
}' "Configurando preferencias de Ricardo"

echo ""
echo "👩 === CREANDO USUARIO DAMARIS ==="
echo ""

# 4. Registrar Damaris
make_request "POST" "/auth/register" '{
    "email": "'$DAMARIS_EMAIL'",
    "password": "'$DAMARIS_PASSWORD'",
    "name": "'$DAMARIS_NAME'",
    "phone": "'$DAMARIS_PHONE'"
}' "Registrando Damaris en Cognito"

# 5. Crear perfil de Damaris
make_request "POST" "/user/profile" '{
    "email": "'$DAMARIS_EMAIL'",
    "name": "'$DAMARIS_NAME'",
    "phone": "'$DAMARIS_PHONE'",
    "birthDate": "1992-08-22",
    "bio": "Amante de la vida, los libros y las aventuras compartidas",
    "interests": ["lectura", "arte", "cocina", "naturaleza"]
}' "Creando perfil de Damaris"

# 6. Crear preferencias de Damaris
make_request "POST" "/user/preferences" '{
    "notifications": true,
    "theme": "dark",
    "language": "es",
    "privacy": {
        "showLocation": true,
        "showOnlineStatus": true
    }
}' "Configurando preferencias de Damaris"

echo ""
echo "💕 === ESTABLECIENDO RELACIÓN DE PAREJA ==="
echo ""

# 7. Ricardo invita a Damaris
make_request "POST" "/couple/invite" '{
    "partnerEmail": "'$DAMARIS_EMAIL'",
    "relationshipType": "dating",
    "message": "¡Hola amor! ¿Te gustaría conectar nuestras cuentas en HopieApp? 💕"
}' "Ricardo invita a Damaris"

# 8. Damaris acepta la invitación (simulamos que acepta)
make_request "POST" "/couple/accept" '{
    "partnerEmail": "'$RICARDO_EMAIL'",
    "accept": true,
    "relationshipType": "dating",
    "startDate": "2023-12-01"
}' "Damaris acepta invitación"

echo ""
echo "🌳 === CREANDO ÁRBOLES VIRTUALES ==="
echo ""

# 9. Ricardo planta su primer árbol
make_request "POST" "/trees" '{
    "userEmail": "'$RICARDO_EMAIL'",
    "name": "Árbol del Amor Eterno",
    "type": "oak",
    "location": "Jardín de nuestros sueños",
    "plantedDate": "2023-12-01T00:00:00.000Z"
}' "Ricardo planta árbol del amor"

# 10. Damaris planta su árbol
make_request "POST" "/trees" '{
    "userEmail": "'$DAMARIS_EMAIL'",
    "name": "Cerezo de la Felicidad",
    "type": "cherry",
    "location": "Rincón de la esperanza",
    "plantedDate": "2023-12-02T00:00:00.000Z"
}' "Damaris planta cerezo de la felicidad"

# 11. Árbol compartido de la pareja
make_request "POST" "/trees" '{
    "userEmail": "'$RICARDO_EMAIL'",
    "name": "Nuestro Árbol Juntos",
    "type": "pine",
    "location": "Corazón compartido",
    "plantedDate": "2023-12-03T00:00:00.000Z",
    "sharedWith": "'$DAMARIS_EMAIL'"
}' "Creando árbol compartido"

echo ""
echo "🎯 === CREANDO PLANES DE VIDA ==="
echo ""

# 12. Plan de vida de Ricardo
make_request "POST" "/lifeplans" '{
    "userEmail": "'$RICARDO_EMAIL'",
    "title": "Construir una relación sólida",
    "description": "Fortalecer nuestra relación y crear recuerdos inolvidables juntos",
    "category": "relationships",
    "targetDate": "2024-12-31",
    "goals": [
        {
            "title": "Citas semanales",
            "description": "Tener una cita romántica cada semana",
            "priority": "high"
        },
        {
            "title": "Viaje romántico",
            "description": "Planear y realizar un viaje especial juntos",
            "priority": "medium"
        }
    ]
}' "Plan de vida de Ricardo"

# 13. Plan de vida de Damaris
make_request "POST" "/lifeplans" '{
    "userEmail": "'$DAMARIS_EMAIL'",
    "title": "Crecimiento personal y en pareja",
    "description": "Desarrollarme como persona mientras fortalezco nuestra relación",
    "category": "personal_growth",
    "targetDate": "2024-08-31",
    "goals": [
        {
            "title": "Comunicación efectiva",
            "description": "Mejorar nuestras habilidades de comunicación",
            "priority": "high"
        },
        {
            "title": "Actividades nuevas",
            "description": "Probar una actividad nueva juntos cada mes",
            "priority": "medium"
        }
    ]
}' "Plan de vida de Damaris"

echo ""
echo "⏰ === CONFIGURANDO RECORDATORIOS ==="
echo ""

# 14. Recordatorio para regar árboles (Ricardo)
make_request "POST" "/schedules" '{
    "userEmail": "'$RICARDO_EMAIL'",
    "title": "Regar nuestros árboles",
    "description": "Recordatorio diario para cuidar nuestros árboles del amor",
    "type": "reminder",
    "scheduledTime": "2024-01-15T08:00:00.000Z",
    "recurrence": "daily",
    "action": {
        "type": "water_tree",
        "data": {
            "message": "¡Es hora de regar nuestros árboles del amor! 🌳💕"
        }
    }
}' "Recordatorio regar árboles Ricardo"

# 15. Recordatorio cita semanal (Damaris)
make_request "POST" "/schedules" '{
    "userEmail": "'$DAMARIS_EMAIL'",
    "title": "Planear cita semanal",
    "description": "Recordatorio para planear nuestra cita romántica de la semana",
    "type": "reminder",
    "scheduledTime": "2024-01-15T18:00:00.000Z",
    "recurrence": "weekly",
    "action": {
        "type": "notification",
        "data": {
            "title": "¡Hora de planear nuestra cita! 💕",
            "message": "¿Qué te parece si planeamos algo especial para esta semana?"
        }
    }
}' "Recordatorio cita semanal Damaris"

echo ""
echo "💬 === CREANDO SALA DE CHAT ==="
echo ""

# 16. Crear sala de chat de la pareja
make_request "POST" "/rooms" '{
    "name": "Ricardo & Damaris 💕",
    "participants": ["'$RICARDO_EMAIL'", "'$DAMARIS_EMAIL'"],
    "type": "couple"
}' "Creando sala de chat de pareja"

echo ""
echo "🎉 === RESUMEN DE CREACIÓN ==="
echo ""
echo "✅ Usuarios creados en Cognito:"
echo "   👨 Ricardo Arrecis Rivera ($RICARDO_EMAIL)"
echo "   👩 Damaris G Coronado ($DAMARIS_EMAIL)"
echo ""
echo "✅ Relación establecida:"
echo "   💕 Pareja activa desde 2023-12-01"
echo "   🌳 3 árboles virtuales plantados"
echo "   🎯 2 planes de vida creados"
echo "   ⏰ 2 recordatorios configurados"
echo "   💬 Sala de chat de pareja creada"
echo ""
echo "🔐 Credenciales de acceso:"
echo "   📧 Email: $RICARDO_EMAIL / $DAMARIS_EMAIL"
echo "   🔑 Password: $RICARDO_PASSWORD"
echo ""
echo "🚀 ¡La pareja está lista para usar HopieApp!"
echo ""
echo "📱 Próximos pasos:"
echo "   1. Los usuarios pueden hacer login en la app"
echo "   2. Confirmar códigos de verificación si es necesario"
echo "   3. Explorar funcionalidades de pareja"
echo "   4. Usar chat en tiempo real"
echo "   5. Cuidar sus árboles virtuales"
echo ""
echo "🔍 Para verificar los datos:"
echo "   aws dynamodb scan --table-name hopie-main-table-dev --region $REGION"
