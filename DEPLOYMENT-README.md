# HopieApp - Guía de Despliegue Seguro

Esta guía te ayudará a desplegar tu aplicación HopieApp de manera segura, evitando rollbacks automáticos que pueden destruir recursos ya creados.

## 🚀 Estrategias Implementadas

### 1. **Políticas de Retención**
- Los recursos críticos (DynamoDB, S3, Cognito) tienen `DeletionPolicy: Retain`
- Esto previene que se eliminen accidentalmente durante rollbacks

### 2. **Despliegue Condicional**
- Las funciones Lambda y WebSocket son opcionales
- Puedes desplegar la infraestructura base primero

### 3. **Versiones y Aliases de Lambda**
- Cada función Lambda tiene versiones y aliases por ambiente
- Despliegue canary en producción para mayor seguridad

### 4. **Dependencias Explícitas**
- `DependsOn` explícitos para controlar el orden de creación
- Reduce fallos por dependencias no resueltas

## 📁 Archivos de Despliegue

- `template.yaml` - Template principal completo
- `template-base.yaml` - Solo infraestructura base (DynamoDB, S3, Cognito)
- `deploy-safe.ps1` - Script de despliegue seguro recomendado
- `deploy-phased.ps1` - Despliegue por fases con confirmación manual
- `deploy.ps1` - Script de despliegue estándar con opciones
- `deploy-config.json` - Configuración por ambiente

## 🛠️ Métodos de Despliegue

### Método 1: Despliegue Seguro (Recomendado)

```powershell
# Solo infraestructura base (DynamoDB, S3, Cognito)
.\deploy-safe.ps1 -Stage dev -BaseOnly

# Despliegue completo incremental
.\deploy-safe.ps1 -Stage dev

# Despliegue completo de una vez (solo si estás seguro)
.\deploy-safe.ps1 -Stage dev -FullStack
```

### Método 2: Despliegue por Fases con Confirmación

```powershell
# Despliegue interactivo por fases
.\deploy-phased.ps1 -Stage dev
```

### Método 3: Despliegue Estándar con Opciones

```powershell
# Despliegue sin funciones Lambda
.\deploy.ps1 -Stage dev -SkipLambdas

# Despliegue sin WebSocket
.\deploy.ps1 -Stage dev -SkipWebSocket

# Despliegue completo con rollback deshabilitado
.\deploy.ps1 -Stage dev -DisableRollback

# Dry run (solo mostrar cambios)
.\deploy.ps1 -Stage dev -DryRun
```

## 🔧 Configuración por Ambiente

Edita `deploy-config.json` para personalizar cada ambiente:

```json
{
  "dev": {
    "enableLambdaFunctions": "true",
    "enableWebSocket": "true",
    "cognitoDomainPrefix": "hopie-app-dev",
    "deploymentStrategy": "AllAtOnce"
  },
  "prod": {
    "enableLambdaFunctions": "true",
    "enableWebSocket": "true", 
    "cognitoDomainPrefix": "hopie-app-prod",
    "deploymentStrategy": "Canary10Percent5Minutes"
  }
}
```

## 🚨 Solución de Problemas

### Si las Funciones Lambda Fallan

1. **No entres en pánico** - La infraestructura base permanece intacta
2. Revisa los logs de CloudFormation:
   ```powershell
   aws cloudformation describe-stack-events --stack-name HopieApp-dev --max-items 10
   ```
3. Intenta desplegar solo las Lambdas:
   ```powershell
   .\deploy.ps1 -Stage dev -SkipWebSocket
   ```

### Si WebSocket Falla

1. Despliega sin WebSocket primero:
   ```powershell
   .\deploy.ps1 -Stage dev -SkipWebSocket
   ```
2. Luego intenta el despliegue completo:
   ```powershell
   .\deploy.ps1 -Stage dev
   ```

### Rollback Manual

Si necesitas hacer rollback manual:

```powershell
# Eliminar stack completo (mantiene base)
aws cloudformation delete-stack --stack-name HopieApp-dev

# Eliminar stack base (¡CUIDADO! Elimina datos)
aws cloudformation delete-stack --stack-name HopieApp-dev-Base
```

## 📋 Checklist Pre-Despliegue

- [ ] AWS CLI configurado con credenciales válidas
- [ ] SAM CLI instalado y actualizado
- [ ] Código de funciones Lambda presente en `src/`
- [ ] Configuración revisada en `deploy-config.json`
- [ ] Backup de datos importantes (si aplica)

## 🎯 Estrategia Recomendada

1. **Primera vez**: Usa `deploy-safe.ps1 -BaseOnly` para crear la infraestructura base
2. **Desarrollo**: Usa `deploy-safe.ps1` para despliegues incrementales
3. **Producción**: Usa `deploy-phased.ps1` para máximo control

## 📞 Comandos Útiles

```powershell
# Ver estado del stack
aws cloudformation describe-stacks --stack-name HopieApp-dev

# Ver eventos recientes
aws cloudformation describe-stack-events --stack-name HopieApp-dev --max-items 5

# Ver outputs del stack
aws cloudformation describe-stacks --stack-name HopieApp-dev --query "Stacks[0].Outputs"

# Listar todos los stacks
aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE
```

## 🔄 Versionado de Lambda

Cada función Lambda se despliega con:
- **Versión**: Inmutable, numerada automáticamente
- **Alias**: Apunta a una versión específica (dev, staging, prod)
- **Despliegue Canary**: En producción para reducir riesgo

Esto permite rollbacks rápidos cambiando el alias sin redesplegar.

---

**¡Importante!** Siempre prueba en `dev` antes de desplegar a `prod`. Los recursos con `DeletionPolicy: Retain` no se eliminarán automáticamente, así que gestiona los costos apropiadamente.
