const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/ingredients')) {
            return await getIngredients(event);
        } else if (httpMethod === 'POST' && path.includes('/ingredients')) {
            return await createIngredient(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/ingredient/')) {
            return await getIngredientById(event);
        } else if (httpMethod === 'PUT' && path.includes('/ingredient/')) {
            return await updateIngredient(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/ingredient/')) {
            return await deleteIngredient(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getIngredients(event) {
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'INGREDIENT'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            ingredients: result.Items || []
        })
    };
}

async function createIngredient(body, event) {
    const ingredientId = `ingredient-${Date.now()}`;
    const { name, category, unit, nutritionalInfo } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA',
            GSI1PK: 'INGREDIENT',
            GSI1SK: ingredientId,
            id: ingredientId,
            name,
            category,
            unit,
            nutritionalInfo,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Ingredient created successfully',
            ingredient: params.Item
        })
    };
}

async function getIngredientById(event) {
    const ingredientId = event.pathParameters?.id || 'ingredient-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            ingredient: result.Item || null
        })
    };
}

async function updateIngredient(body, event) {
    const ingredientId = event.pathParameters?.id || 'ingredient-123';
    const { name, category, unit, nutritionalInfo } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #name = :name, category = :category, unit = :unit, nutritionalInfo = :nutritionalInfo, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':category': category,
            ':unit': unit,
            ':nutritionalInfo': nutritionalInfo,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Ingredient updated successfully',
            ingredient: result.Attributes
        })
    };
}

async function deleteIngredient(event) {
    const ingredientId = event.pathParameters?.id || 'ingredient-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Ingredient deleted successfully'
        })
    };
}
