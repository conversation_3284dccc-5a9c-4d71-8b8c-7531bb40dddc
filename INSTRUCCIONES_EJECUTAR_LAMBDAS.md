# 🚀 Instrucciones para Crear TODAS las Funciones Lambda desde CLI

## 📋 Requisitos Previos

1. **AWS CLI instalado y configurado**:
```bash
aws configure
# Ingresa tu Access Key ID, Secret Access Key, Region (us-east-2)
```

2. **Verificar que tienes permisos**:
```bash
aws sts get-caller-identity
```

3. **Verificar que el rol IAM existe**:
```bash
aws iam get-role --role-name hopie-lambda-execution-role
```

## 🎯 Opción 1: Script Completo (Recomendado)

### Paso 1: Descargar y preparar el script principal
```bash
# Crear directorio de trabajo
mkdir hopie-lambda-setup
cd hopie-lambda-setup

# Descargar el script (o copiarlo manualmente)
curl -o create-all-lambdas.sh https://raw.githubusercontent.com/tu-repo/CREATE_ALL_14_LAMBDAS.sh

# O crear el archivo manualmente:
nano create-all-lambdas.sh
# Copiar todo el contenido del archivo CREATE_ALL_14_LAMBDAS.sh
```

### Paso 2: Configurar permisos y ejecutar
```bash
# Dar permisos de ejecución
chmod +x create-all-lambdas.sh

# Ejecutar el script
./create-all-lambdas.sh
```

### Paso 3: Verificar que se crearon
```bash
# Listar todas las funciones Lambda de HopieApp
aws lambda list-functions --query 'Functions[?starts_with(FunctionName, `hopie-`)].FunctionName' --output table
```

## 🎯 Opción 2: Crear Funciones Una por Una

### Función 1: AuthFunction
```bash
# Crear directorio
mkdir -p lambda-functions/hopie-auth-dev

# Crear el código
cat > lambda-functions/hopie-auth-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'POST' && path.includes('/login')) {
            return await handleLogin(requestBody);
        } else if (httpMethod === 'POST' && path.includes('/register')) {
            return await handleRegister(requestBody);
        } else if (httpMethod === 'POST' && path.includes('/refresh')) {
            return await handleRefreshToken(requestBody);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function handleLogin(body) {
    const { email, password } = body;
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Login successful',
            token: 'mock-jwt-token',
            user: { email, id: 'user-123' }
        })
    };
}

async function handleRegister(body) {
    const { email, password, name } = body;
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'User registered successfully',
            user: { email, name, id: 'user-new-123' }
        })
    };
}

async function handleRefreshToken(body) {
    const { refreshToken } = body;
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Token refreshed',
            token: 'new-mock-jwt-token'
        })
    };
}
EOF

# Crear ZIP
cd lambda-functions/hopie-auth-dev
zip -r ../../hopie-auth-dev.zip .
cd ../..

# Crear función Lambda
aws lambda create-function \
    --function-name hopie-auth-dev \
    --runtime nodejs18.x \
    --role arn:aws:iam::864899858226:role/hopie-lambda-execution-role \
    --handler index.handler \
    --zip-file fileb://hopie-auth-dev.zip \
    --description "HopieApp Authentication Function" \
    --timeout 30 \
    --memory-size 256 \
    --environment Variables="{TABLE_NAME=hopie-main-table-dev,USER_POOL_ID=us-east-2_gTZjXep7j,CLIENT_ID=63604alcg1rvili3ksfan61fid,REGION=us-east-2}" \
    --region us-east-2

# Limpiar
rm hopie-auth-dev.zip
```

## 🔧 Variables de Entorno Configuradas

**Todas las funciones tendrán estas variables:**
- `TABLE_NAME`: `hopie-main-table-dev`
- `USER_POOL_ID`: `us-east-2_gTZjXep7j`
- `CLIENT_ID`: `63604alcg1rvili3ksfan61fid`
- `REGION`: `us-east-2`

**Funciones con variables adicionales:**
- `hopie-image-dev`: + `BUCKET_NAME=hopie-app-assets-dev-123456`
- `hopie-image-processor-dev`: + `BUCKET_NAME=hopie-app-assets-dev-123456`

## 📋 Lista de las 14 Funciones a Crear

1. ✅ `hopie-auth-dev` - Autenticación
2. ✅ `hopie-user-dev` - Gestión de usuarios
3. ⏳ `hopie-couple-dev` - Gestión de parejas
4. ⏳ `hopie-tree-dev` - Árbol genealógico
5. ⏳ `hopie-questions-dev` - Preguntas diarias
6. ⏳ `hopie-lifeplan-dev` - Plan de vida
7. ⏳ `hopie-places-dev` - Lugares favoritos
8. ⏳ `hopie-location-dev` - Geolocalización
9. ⏳ `hopie-stats-dev` - Estadísticas
10. ⏳ `hopie-image-dev` - Gestión de imágenes
11. ⏳ `hopie-notification-dev` - Notificaciones
12. ⏳ `hopie-chat-dev` - Chat WebSocket
13. ⏳ `hopie-image-processor-dev` - Procesador de imágenes
14. ⏳ `hopie-scheduler-dev` - Tareas programadas

## 🚨 Solución de Problemas

### Error: "Role does not exist"
```bash
# Verificar que el rol existe
aws iam get-role --role-name hopie-lambda-execution-role

# Si no existe, crearlo:
aws iam create-role \
    --role-name hopie-lambda-execution-role \
    --assume-role-policy-document '{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "lambda.amazonaws.com"
                },
                "Action": "sts:AssumeRole"
            }
        ]
    }'

# Agregar políticas
aws iam attach-role-policy --role-name hopie-lambda-execution-role --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
aws iam attach-role-policy --role-name hopie-lambda-execution-role --policy-arn arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess
aws iam attach-role-policy --role-name hopie-lambda-execution-role --policy-arn arn:aws:iam::aws:policy/AmazonS3FullAccess
```

### Error: "Function already exists"
```bash
# Eliminar función existente
aws lambda delete-function --function-name hopie-auth-dev

# Luego volver a crear
```

### Verificar que todo funciona
```bash
# Probar una función
aws lambda invoke \
    --function-name hopie-auth-dev \
    --payload '{"httpMethod":"POST","path":"/auth/login","body":"{\"email\":\"<EMAIL>\",\"password\":\"123456\"}"}' \
    response.json

# Ver la respuesta
cat response.json
```

## 🎉 Resultado Final

Después de ejecutar todo, tendrás:
- ✅ 14 funciones Lambda creadas
- ✅ Todas con las variables de entorno correctas
- ✅ Todas con el rol IAM correcto
- ✅ Todas listas para conectar al API Gateway

**Siguiente paso:** Crear el API Gateway y conectarlo con estas funciones usando los archivos de configuración manual.
