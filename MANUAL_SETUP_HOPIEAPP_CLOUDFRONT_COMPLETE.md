# 🚀 HopieApp - CloudFront, EventBridge y Configuración Final

## 8. CloudFront - CONFIGURACIÓN COMPLETA PASO A PASO

### 8.1 Crear Distribución CloudFront
1. Ve a **AWS Console** → Busca **CloudFront** → Click **CloudFront**
2. Click **Create distribution**

### 8.2 Configurar Origin
**Origin settings:**
1. **Origin domain**: 
   - Click en el campo y selecciona tu bucket: `hopie-app-assets-dev-123456.s3.us-east-2.amazonaws.com`
2. **Origin path**: (dejar vacío)
3. **Name**: `hopie-app-assets-dev-123456.s3.us-east-2.amazonaws.com` (se llena automáticamente)
4. **Origin access**: 
   - ✅ **Origin access control settings (recommended)**
5. **Origin access control**: 
   - Click **Create control setting**
   - **Name**: `hopie-oac-dev`
   - **Description**: `OAC for HopieApp assets`
   - **Origin type**: **S3**
   - **Signing behavior**: **Sign requests (recommended)**
   - **Origin access control**: **All viewer requests**
   - Click **Create**
6. **Additional settings**:
   - **Add custom header**: (no agregar nada)
   - **Enable Origin Shield**: ❌ (NO marcar)

### 8.3 Configurar Default Cache Behavior
**Default cache behavior:**
1. **Path pattern**: `Default (*)` (no cambiar)
2. **Compress objects automatically**: ✅ (SÍ marcar)
3. **Viewer protocol policy**: **Redirect HTTP to HTTPS**
4. **Allowed HTTP methods**: 
   - ✅ **GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE**
5. **Restrict viewer access**: ❌ (NO marcar)
6. **Cache key and origin requests**:
   - **Cache policy**: **CachingOptimized**
   - **Origin request policy**: **None**
   - **Response headers policy**: **None**
7. **Function associations**: (no agregar nada)

### 8.4 Configurar Settings
**Settings:**
1. **Price class**: **Use all edge locations (best performance)**
2. **AWS WAF web ACL**: **None**
3. **Alternate domain name (CNAME)**: (dejar vacío por ahora)
4. **Custom SSL certificate**: (dejar por defecto)
5. **Supported HTTP versions**: **HTTP/2 and HTTP/1.1**
6. **Default root object**: `index.html`
7. **Standard logging**: ❌ (NO marcar)
8. **IPv6**: ✅ (SÍ marcar)
9. **Description**: `HopieApp CloudFront distribution for assets`

### 8.5 Create Distribution
1. Click **Create distribution**
2. **IMPORTANTE**: Aparecerá un banner azul con instrucciones para actualizar la bucket policy
3. **Copy policy** → Copia la política que aparece

### 8.6 Actualizar Bucket Policy
1. Ve a **S3** → Tu bucket `hopie-app-assets-dev-123456`
2. **Permissions** → **Bucket policy** → **Edit**
3. **REEMPLAZA** toda la política existente con la que copiaste de CloudFront
4. Click **Save changes**

### 8.7 Obtener CloudFront URL
1. Ve a **CloudFront** → **Distributions**
2. Tu distribución aparecerá con estado **Deploying**
3. **Domain name**: `dXXXXXXXXXX.cloudfront.net`
4. **ANOTA ESTA URL** - la necesitarás para Flutter
5. **IMPORTANTE**: Espera a que el estado cambie a **Enabled** (puede tomar 15-20 minutos)

---

## 9. EventBridge - CONFIGURACIÓN COMPLETA PASO A PASO

### 9.1 Crear Regla para Pregunta Diaria
1. Ve a **AWS Console** → Busca **EventBridge** → Click **Amazon EventBridge**
2. En el panel izquierdo, click **Rules**
3. Click **Create rule**

**Step 1: Define rule detail**
4. **Name**: `hopie-daily-question-rule`
5. **Description**: `Generate daily question for HopieApp users`
6. **Event bus**: **default**
7. **Rule type**: ✅ **Schedule**
8. Click **Next**

**Step 2: Define schedule**
9. **Schedule pattern**: ✅ **A fine-grained schedule that runs at a specific time**
10. **Schedule expression**: ✅ **Cron expression**
11. **Cron expression**: `0 9 * * ? *`
    - **Explicación**: Ejecuta todos los días a las 9:00 AM UTC
12. **Flexible time window**: ❌ **Off**
13. Click **Next**

**Step 3: Select target(s)**
14. **Target types**: ✅ **AWS service**
15. **Select a target**: **Lambda function**
16. **Function**: `hopie-scheduler-dev`
17. **Version/Alias**: **$LATEST**
18. **Additional settings**: (dejar por defecto)
19. Click **Next**

**Step 4: Configure tags**
20. **Tags**: (opcional, puedes agregar)
    - **Key**: `Project`, **Value**: `HopieApp`
    - **Key**: `Environment`, **Value**: `dev`
21. Click **Next**

**Step 5: Review and create**
22. Revisar toda la configuración
23. Click **Create rule**

### 9.2 Crear Regla para Cálculo de Streaks
1. **Rules** → **Create rule**

**Step 1: Define rule detail**
2. **Name**: `hopie-streak-calculation-rule`
3. **Description**: `Calculate user streaks for HopieApp`
4. **Event bus**: **default**
5. **Rule type**: ✅ **Schedule**
6. Click **Next**

**Step 2: Define schedule**
7. **Schedule pattern**: ✅ **A fine-grained schedule that runs at a specific time**
8. **Schedule expression**: ✅ **Cron expression**
9. **Cron expression**: `0 1 * * ? *`
   - **Explicación**: Ejecuta todos los días a la 1:00 AM UTC
10. Click **Next**

**Step 3: Select target(s)**
11. **Target types**: ✅ **AWS service**
12. **Select a target**: **Lambda function**
13. **Function**: `hopie-scheduler-dev`
14. Click **Next**

**Step 4: Configure tags**
15. **Tags**: (opcional)
    - **Key**: `Project`, **Value**: `HopieApp`
    - **Key**: `Environment`, **Value**: `dev`
16. Click **Next**

**Step 5: Review and create**
17. Click **Create rule**

---

## 10. CONFIGURACIÓN FINAL PARA FLUTTER

### 10.1 URLs Finales de tu Aplicación

**Después de completar todo, tendrás estas URLs:**

```dart
class ApiConfig {
  // API REST
  static const String baseUrl = 'https://XXXXXXXXXX.execute-api.us-east-2.amazonaws.com/dev';
  
  // WebSocket
  static const String websocketUrl = 'wss://YYYYYYYYYY.execute-api.us-east-2.amazonaws.com/dev';
  
  // CloudFront CDN
  static const String cdnUrl = 'https://dZZZZZZZZZ.cloudfront.net';
  
  // Cognito
  static const String userPoolId = 'us-east-2_XXXXXXXXX';
  static const String clientId = 'abcdef123456789';
  static const String region = 'us-east-2';
  static const String cognitoDomain = 'hopie-app-dev-123456.auth.us-east-2.amazoncognito.com';
  
  // S3
  static const String bucketName = 'hopie-app-assets-dev-123456';
}
```

### 10.2 Endpoints Disponibles

**Autenticación (sin autorización):**
```
POST ${baseUrl}/auth/login
POST ${baseUrl}/auth/register  
POST ${baseUrl}/auth/refresh
```

**Usuarios (con autorización Cognito):**
```
GET  ${baseUrl}/users/profile
PUT  ${baseUrl}/users/profile
GET  ${baseUrl}/users/preferences
PUT  ${baseUrl}/users/preferences
```

**Parejas:**
```
POST ${baseUrl}/couples/create
GET  ${baseUrl}/couples/info
PUT  ${baseUrl}/couples/update
POST ${baseUrl}/couples/invite
```

**Árbol genealógico:**
```
GET    ${baseUrl}/trees/family
POST   ${baseUrl}/trees/add-member
PUT    ${baseUrl}/trees/update-member
DELETE ${baseUrl}/trees/remove-member
```

**Preguntas diarias:**
```
GET  ${baseUrl}/questions/daily
POST ${baseUrl}/questions/answer
GET  ${baseUrl}/questions/history
GET  ${baseUrl}/questions/categories
```

**Plan de vida:**
```
GET    ${baseUrl}/lifeplan/goals
POST   ${baseUrl}/lifeplan/create
PUT    ${baseUrl}/lifeplan/update
DELETE ${baseUrl}/lifeplan/delete
```

**Lugares favoritos:**
```
GET    ${baseUrl}/places/favorites
POST   ${baseUrl}/places/add
PUT    ${baseUrl}/places/update
DELETE ${baseUrl}/places/remove
```

**Geolocalización:**
```
POST ${baseUrl}/location/update
GET  ${baseUrl}/location/current
GET  ${baseUrl}/location/history
POST ${baseUrl}/location/share
```

**Estadísticas:**
```
GET  ${baseUrl}/stats/dashboard
GET  ${baseUrl}/stats/streaks
GET  ${baseUrl}/stats/activity
POST ${baseUrl}/stats/update-streak
```

**Imágenes:**
```
POST   ${baseUrl}/images/upload
GET    ${baseUrl}/images/gallery
POST   ${baseUrl}/images/save-metadata
DELETE ${baseUrl}/images/delete
```

**Notificaciones:**
```
GET ${baseUrl}/notifications/list
POST ${baseUrl}/notifications/mark-read
POST ${baseUrl}/notifications/send
PUT  ${baseUrl}/notifications/preferences
```

### 10.3 Configuración de Headers para Flutter

```dart
Map<String, String> getHeaders({bool requiresAuth = true}) {
  Map<String, String> headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  if (requiresAuth) {
    // Agregar token de Cognito
    headers['Authorization'] = 'Bearer $cognitoToken';
  }
  
  return headers;
}
```

### 10.4 Ejemplo de Uso en Flutter

```dart
// Para endpoints sin autenticación
final response = await http.post(
  Uri.parse('${ApiConfig.baseUrl}/auth/login'),
  headers: getHeaders(requiresAuth: false),
  body: jsonEncode({
    'email': email,
    'password': password,
  }),
);

// Para endpoints con autenticación
final response = await http.get(
  Uri.parse('${ApiConfig.baseUrl}/users/profile'),
  headers: getHeaders(requiresAuth: true),
);
```

---

## 🎉 RESUMEN FINAL

**Has configurado exitosamente:**

✅ **DynamoDB**: 1 tabla con 3 GSIs y streams habilitados
✅ **S3**: 1 bucket con CORS y políticas configuradas  
✅ **Cognito**: User Pool con Google OAuth configurado
✅ **IAM**: Rol de ejecución para Lambda con todos los permisos
✅ **Lambda**: 14 funciones con código y variables de entorno
✅ **API Gateway REST**: 11 endpoints con autorización configurada
✅ **API Gateway WebSocket**: 4 rutas para chat en tiempo real
✅ **CloudFront**: Distribución CDN para assets estáticos
✅ **EventBridge**: 2 reglas para tareas programadas

**Total de recursos AWS:** ~30 recursos configurados manualmente

**Tu aplicación HopieApp está 100% lista para ser consumida desde Flutter!** 🚀
