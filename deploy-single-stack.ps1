param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipLambdas,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipWebSocket,
    
    [Parameter(Mandatory=$false)]
    [string]$GoogleClientId = '',
    
    [Parameter(Mandatory=$false)]
    [string]$GoogleClientSecret = '',
    
    [Parameter(Mandatory=$false)]
    [string]$StackName = "HopieApp-$Stage"
)

# Colores para output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput $Blue "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "❌ $message"
}

Write-Info "🚀 Desplegando HopieApp en un solo stack"
Write-Info "Stage: $Stage"
Write-Info "Stack: $StackName"

# Verificar herramientas
Write-Info "Verificando herramientas..."
try {
    $samVersion = sam --version
    Write-Success "SAM CLI: $samVersion"
    
    $awsIdentity = aws sts get-caller-identity --output json | ConvertFrom-Json
    Write-Success "AWS Account: $($awsIdentity.Account)"
} catch {
    Write-Error "Error verificando herramientas. Asegúrate de tener SAM CLI y AWS CLI configurados."
    exit 1
}

# Preparar parámetros
$parameters = @()
$parameters += "Stage=$Stage"
$parameters += "CognitoDomainPrefix=hopie-app-$Stage"

if ($SkipLambdas) {
    $parameters += "EnableLambdaFunctions=false"
    Write-Warning "Las funciones Lambda serán omitidas"
} else {
    $parameters += "EnableLambdaFunctions=true"
}

if ($SkipWebSocket) {
    $parameters += "EnableWebSocket=false"
    Write-Warning "WebSocket API será omitido"
} else {
    $parameters += "EnableWebSocket=true"
}

# Configuración de Google Auth
if ($GoogleClientId -and $GoogleClientSecret) {
    $parameters += "GoogleClientId=$GoogleClientId"
    $parameters += "GoogleClientSecret=$GoogleClientSecret"
    Write-Success "Autenticación con Google habilitada"
} else {
    Write-Info "Autenticación con Google no configurada (solo Cognito nativo)"
    Write-Info "Para habilitar Google Auth, proporciona GoogleClientId y GoogleClientSecret"
}

# Construir aplicación
Write-Info "Construyendo aplicación..."
try {
    sam build
    if ($LASTEXITCODE -ne 0) {
        throw "Error en sam build"
    }
    Write-Success "Aplicación construida exitosamente"
} catch {
    Write-Error "Error al construir la aplicación: $_"
    exit 1
}

# Comando SAM
$samCommand = @(
    "sam", "deploy"
    "--template-file", "template.yaml"
    "--stack-name", $StackName
    "--capabilities", "CAPABILITY_IAM", "CAPABILITY_AUTO_EXPAND"
    "--disable-rollback"
    "--parameter-overrides"
) + $parameters

Write-Info "Comando a ejecutar:"
Write-ColorOutput $Blue ($samCommand -join " ")

# Confirmar despliegue
$confirmation = Read-Host "¿Continuar con el despliegue? (y/N)"
if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Warning "Despliegue cancelado por el usuario"
    exit 0
}

# Ejecutar despliegue
Write-Info "Iniciando despliegue con rollback deshabilitado..."
Write-Warning "Si algo falla, los recursos creados exitosamente NO se eliminarán"

try {
    & $samCommand[0] $samCommand[1..($samCommand.Length-1)]
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "🎉 ¡Despliegue completado exitosamente!"
        
        # Mostrar outputs del stack
        Write-Info "Outputs del stack:"
        aws cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs" --output table
        
        # Información adicional
        Write-Info "`nInformación importante:"
        if ($GoogleClientId -and $GoogleClientSecret) {
            Write-Success "✅ Autenticación con Google configurada"
            Write-Info "   - Los usuarios pueden iniciar sesión con Google"
            Write-Info "   - También pueden crear cuentas nativas en Cognito"
        } else {
            Write-Warning "⚠️  Solo autenticación nativa de Cognito"
            Write-Info "   - Para agregar Google Auth, ejecuta nuevamente con -GoogleClientId y -GoogleClientSecret"
        }
        
        Write-Info "`nPróximos pasos:"
        Write-Info "1. Configura tu aplicación frontend con las URLs mostradas arriba"
        Write-Info "2. Si usas Google Auth, configura las URLs de callback en Google Cloud Console"
        Write-Info "3. Prueba la autenticación y las APIs"
        
    } else {
        Write-Error "El despliegue falló con código de salida: $LASTEXITCODE"
        Write-Warning "Los recursos creados exitosamente permanecen intactos (no hubo rollback)"
        
        # Mostrar eventos del stack para debugging
        Write-Info "Últimos eventos del stack:"
        aws cloudformation describe-stack-events --stack-name $StackName --max-items 10 --output table
        
        Write-Info "`nPara solucionar problemas:"
        Write-Info "1. Revisa los eventos arriba para identificar qué falló"
        Write-Info "2. Corrige el problema en el código/template"
        Write-Info "3. Ejecuta el script nuevamente"
        Write-Info "4. Los recursos exitosos no se recrearán"
        
        exit $LASTEXITCODE
    }
} catch {
    Write-Error "Error durante el despliegue: $_"
    exit 1
}

Write-Info "`nComandos útiles:"
Write-Info "# Ver estado del stack:"
Write-ColorOutput $Blue "aws cloudformation describe-stacks --stack-name $StackName"
Write-Info "`n# Ver eventos recientes:"
Write-ColorOutput $Blue "aws cloudformation describe-stack-events --stack-name $StackName --max-items 5"
Write-Info "`n# Eliminar stack (si es necesario):"
Write-ColorOutput $Yellow "aws cloudformation delete-stack --stack-name $StackName"
