#!/bin/bash

# 🚀 Script para crear hopie-scheduler-dev y poblar la base de datos con datos de prueba
# Incluye función scheduler y datos de prueba para todas las entidades

TABLE_NAME="hopie-main-table-dev"
USER_POOL_ID="us-east-2_gTZjXep7j"
CLIENT_ID="63604alcg1rvili3ksfan61fid"
REGION="us-east-2"
BUCKET_NAME="hopie-app-assets-dev-123456"
ROLE_ARN="arn:aws:iam::864899858226:role/hopie-lambda-execution-role"

echo "🚀 Creando hopie-scheduler-dev y poblando datos de prueba..."
echo "📋 Variables configuradas:"
echo "   TABLE_NAME: $TABLE_NAME"
echo "   USER_POOL_ID: $USER_POOL_ID"
echo "   CLIENT_ID: $CLIENT_ID"
echo "   REGION: $REGION"
echo "   BUCKET_NAME: $BUCKET_NAME"
echo "   ROLE_ARN: $ROLE_ARN"
echo ""

# Función para crear Lambda
create_lambda() {
    local FUNCTION_NAME=$1
    local DESCRIPTION=$2
    local ENV_VARS=$3
    
    echo "📦 Creando función: $FUNCTION_NAME"
    
    # Crear archivo ZIP temporal
    cd lambda-functions/$FUNCTION_NAME
    zip -r ../../${FUNCTION_NAME}.zip . > /dev/null 2>&1
    cd ../..
    
    # Crear función Lambda
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://${FUNCTION_NAME}.zip \
        --description "$DESCRIPTION" \
        --timeout 30 \
        --memory-size 256 \
        --environment Variables="$ENV_VARS" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $FUNCTION_NAME creada exitosamente"
    else
        echo "   ❌ Error creando $FUNCTION_NAME"
    fi
    
    # Limpiar archivo ZIP
    rm ${FUNCTION_NAME}.zip
}

# Crear directorio para funciones
mkdir -p lambda-functions

# Scheduler Function
mkdir -p lambda-functions/hopie-scheduler-dev
cat > lambda-functions/hopie-scheduler-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const eventbridge = new AWS.EventBridge();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Scheduler Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/schedules')) {
            return await getSchedules(event);
        } else if (httpMethod === 'POST' && path.includes('/schedules')) {
            return await createSchedule(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/schedule/')) {
            return await getScheduleById(event);
        } else if (httpMethod === 'PUT' && path.includes('/schedule/')) {
            return await updateSchedule(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/schedule/')) {
            return await deleteSchedule(event);
        } else if (httpMethod === 'POST' && path.includes('/schedule/trigger')) {
            return await triggerSchedule(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/schedule/upcoming')) {
            return await getUpcomingSchedules(event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getSchedules(event) {
    const userId = 'user-123';
    const type = event.queryStringParameters?.type || 'all';
    
    let params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'SCHEDULE#'
        }
    };
    
    if (type !== 'all') {
        params.FilterExpression = '#type = :type';
        params.ExpressionAttributeNames = { '#type': 'type' };
        params.ExpressionAttributeValues[':type'] = type;
    }
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            schedules: result.Items || []
        })
    };
}

async function createSchedule(body, event) {
    const userId = 'user-123';
    const scheduleId = `schedule-${Date.now()}`;
    const { title, description, type, scheduledTime, recurrence, action, isActive } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`,
            GSI1PK: 'SCHEDULE',
            GSI1SK: scheduleId,
            id: scheduleId,
            userId,
            title,
            description,
            type: type || 'reminder',
            scheduledTime,
            recurrence: recurrence || 'none',
            action: action || {},
            isActive: isActive !== false,
            lastTriggered: null,
            nextTrigger: scheduledTime,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule created successfully',
            schedule: params.Item
        })
    };
}

async function getScheduleById(event) {
    const userId = 'user-123';
    const scheduleId = event.pathParameters?.id || 'schedule-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            schedule: result.Item || null
        })
    };
}

async function updateSchedule(body, event) {
    const userId = 'user-123';
    const scheduleId = event.pathParameters?.id || 'schedule-123';
    const { title, description, scheduledTime, recurrence, action, isActive } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        },
        UpdateExpression: 'SET title = :title, description = :description, scheduledTime = :scheduledTime, recurrence = :recurrence, action = :action, isActive = :isActive, nextTrigger = :nextTrigger, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':title': title,
            ':description': description,
            ':scheduledTime': scheduledTime,
            ':recurrence': recurrence,
            ':action': action,
            ':isActive': isActive,
            ':nextTrigger': scheduledTime,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule updated successfully',
            schedule: result.Attributes
        })
    };
}

async function deleteSchedule(event) {
    const userId = 'user-123';
    const scheduleId = event.pathParameters?.id || 'schedule-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule deleted successfully'
        })
    };
}

async function triggerSchedule(body, event) {
    const userId = 'user-123';
    const { scheduleId } = body;
    
    // Get schedule
    const getParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        }
    };
    
    const scheduleResult = await dynamodb.get(getParams).promise();
    const schedule = scheduleResult.Item;
    
    if (!schedule) {
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Schedule not found' })
        };
    }
    
    // Execute the scheduled action
    let actionResult = {};
    
    switch (schedule.action.type) {
        case 'notification':
            actionResult = await createNotification(schedule.action.data);
            break;
        case 'reminder':
            actionResult = await createReminder(schedule.action.data);
            break;
        case 'water_tree':
            actionResult = await waterTree(schedule.action.data);
            break;
        default:
            actionResult = { message: 'Unknown action type' };
    }
    
    // Update last triggered time
    const updateParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        },
        UpdateExpression: 'SET lastTriggered = :lastTriggered, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':lastTriggered': new Date().toISOString(),
            ':updatedAt': new Date().toISOString()
        }
    };
    
    await dynamodb.update(updateParams).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule triggered successfully',
            actionResult
        })
    };
}

async function getUpcomingSchedules(event) {
    const userId = 'user-123';
    const hours = parseInt(event.queryStringParameters?.hours || '24');
    
    const now = new Date();
    const futureTime = new Date(now.getTime() + (hours * 60 * 60 * 1000));
    
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        FilterExpression: 'isActive = :isActive AND nextTrigger BETWEEN :now AND :future',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'SCHEDULE#',
            ':isActive': true,
            ':now': now.toISOString(),
            ':future': futureTime.toISOString()
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            upcomingSchedules: result.Items || [],
            timeframe: `${hours} hours`
        })
    };
}

async function createNotification(data) {
    const notificationId = `notification-${Date.now()}`;
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${data.userId}`,
            SK: `NOTIFICATION#${notificationId}`,
            id: notificationId,
            title: data.title,
            message: data.message,
            type: 'scheduled',
            read: false,
            createdAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    return { notificationId, message: 'Notification created' };
}

async function createReminder(data) {
    // Similar to notification but with reminder-specific logic
    return await createNotification(data);
}

async function waterTree(data) {
    const { treeId, waterAmount } = data;
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${data.userId}`,
            SK: `TREE#${treeId}`
        },
        UpdateExpression: 'SET waterLevel = waterLevel + :waterAmount, lastWatered = :lastWatered',
        ExpressionAttributeValues: {
            ':waterAmount': waterAmount || 20,
            ':lastWatered': new Date().toISOString()
        }
    };
    
    await dynamodb.update(params).promise();
    return { treeId, message: 'Tree watered automatically' };
}
EOF

echo "📁 Creando hopie-scheduler-dev..."
echo ""

# Crear la función Lambda
echo "🔄 Creando hopie-scheduler-dev..."
create_lambda "hopie-scheduler-dev" "HopieApp Scheduler Function" "{TABLE_NAME=$TABLE_NAME}"

echo ""
echo "🎉 ¡Función hopie-scheduler-dev creada exitosamente!"
echo ""

# Poblar base de datos con datos de prueba
echo "📊 Poblando base de datos con datos de prueba..."
echo ""

# Función para crear datos de prueba
populate_test_data() {
    echo "🔄 Insertando datos de prueba en DynamoDB..."

    # Crear archivo JSON temporal con todos los datos
    cat > test_data.json << 'TESTDATA'
{
    "RequestItems": {
        "hopie-main-table-dev": [
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "USER#user-123"},
                        "SK": {"S": "PROFILE"},
                        "id": {"S": "user-123"},
                        "name": {"S": "Juan Pérez"},
                        "email": {"S": "<EMAIL>"},
                        "phone": {"S": "+1234567890"},
                        "birthDate": {"S": "1990-05-15"},
                        "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "USER#user-456"},
                        "SK": {"S": "PROFILE"},
                        "id": {"S": "user-456"},
                        "name": {"S": "María García"},
                        "email": {"S": "<EMAIL>"},
                        "phone": {"S": "+1234567891"},
                        "birthDate": {"S": "1992-08-22"},
                        "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "USER#user-789"},
                        "SK": {"S": "PROFILE"},
                        "id": {"S": "user-789"},
                        "name": {"S": "Carlos López"},
                        "email": {"S": "<EMAIL>"},
                        "phone": {"S": "+1234567892"},
                        "birthDate": {"S": "1988-12-10"},
                        "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "RECIPE#recipe-001"},
                        "SK": {"S": "METADATA"},
                        "GSI1PK": {"S": "RECIPE"},
                        "GSI1SK": {"S": "recipe-001"},
                        "id": {"S": "recipe-001"},
                        "title": {"S": "Pasta Carbonara"},
                        "description": {"S": "Deliciosa pasta italiana con huevo y panceta"},
                        "ingredients": {"L": [
                            {"S": "400g pasta"},
                            {"S": "200g panceta"},
                            {"S": "4 huevos"},
                            {"S": "100g queso parmesano"}
                        ]},
                        "instructions": {"L": [
                            {"S": "Cocinar la pasta al dente"},
                            {"S": "Freír la panceta hasta dorar"},
                            {"S": "Mezclar huevos con queso"},
                            {"S": "Combinar todo fuera del fuego"}
                        ]},
                        "cookingTime": {"N": "20"},
                        "difficulty": {"S": "medium"},
                        "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "RECIPE#recipe-002"},
                        "SK": {"S": "METADATA"},
                        "GSI1PK": {"S": "RECIPE"},
                        "GSI1SK": {"S": "recipe-002"},
                        "id": {"S": "recipe-002"},
                        "title": {"S": "Ensalada César"},
                        "description": {"S": "Fresca ensalada con pollo y aderezo césar"},
                        "ingredients": {"L": [
                            {"S": "1 lechuga romana"},
                            {"S": "200g pechuga de pollo"},
                            {"S": "50g queso parmesano"},
                            {"S": "Crutones"},
                            {"S": "Aderezo césar"}
                        ]},
                        "instructions": {"L": [
                            {"S": "Lavar y cortar la lechuga"},
                            {"S": "Cocinar el pollo a la plancha"},
                            {"S": "Preparar crutones"},
                            {"S": "Mezclar con aderezo"}
                        ]},
                        "cookingTime": {"N": "15"},
                        "difficulty": {"S": "easy"},
                        "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "RECIPE#recipe-003"},
                        "SK": {"S": "METADATA"},
                        "GSI1PK": {"S": "RECIPE"},
                        "GSI1SK": {"S": "recipe-003"},
                        "id": {"S": "recipe-003"},
                        "title": {"S": "Paella Valenciana"},
                        "description": {"S": "Tradicional paella española con mariscos"},
                        "ingredients": {"L": [
                            {"S": "400g arroz bomba"},
                            {"S": "500g mariscos mixtos"},
                            {"S": "200g judías verdes"},
                            {"S": "1 pimiento rojo"},
                            {"S": "Azafrán"}
                        ]},
                        "instructions": {"L": [
                            {"S": "Sofreír verduras"},
                            {"S": "Añadir arroz y caldo"},
                            {"S": "Incorporar mariscos"},
                            {"S": "Cocinar 18 minutos"}
                        ]},
                        "cookingTime": {"N": "45"},
                        "difficulty": {"S": "hard"},
                        "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
                    }
                }
            }
        ]
    }
}
TESTDATA

    # Ejecutar batch write
    aws dynamodb batch-write-item --request-items file://test_data.json --region $REGION

    if [ $? -eq 0 ]; then
        echo "   ✅ Primer lote de datos insertado exitosamente"
    else
        echo "   ❌ Error insertando primer lote de datos"
    fi

    # Limpiar archivo temporal
    rm test_data.json
}

# Función para crear más datos de prueba
populate_more_data() {
    echo "🔄 Insertando más datos de prueba..."

    cat > more_data.json << 'MOREDATA'
{
    "RequestItems": {
        "hopie-main-table-dev": [
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "USER#user-123"},
                        "SK": {"S": "COUPLE#couple-001"},
                        "GSI1PK": {"S": "COUPLE"},
                        "GSI1SK": {"S": "couple-001"},
                        "id": {"S": "couple-001"},
                        "userId": {"S": "user-123"},
                        "partnerEmail": {"S": "<EMAIL>"},
                        "relationshipType": {"S": "dating"},
                        "startDate": {"S": "2023-12-01"},
                        "status": {"S": "active"},
                        "createdAt": {"S": "2023-12-01T00:00:00.000Z"},
                        "updatedAt": {"S": "2023-12-01T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "QUESTION#question-001"},
                        "SK": {"S": "METADATA"},
                        "GSI1PK": {"S": "QUESTION"},
                        "GSI1SK": {"S": "question-001"},
                        "id": {"S": "question-001"},
                        "text": {"S": "¿Cuál es tu comida favorita?"},
                        "category": {"S": "food"},
                        "type": {"S": "open"},
                        "difficulty": {"S": "easy"},
                        "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "QUESTION#question-002"},
                        "SK": {"S": "METADATA"},
                        "GSI1PK": {"S": "QUESTION"},
                        "GSI1SK": {"S": "question-002"},
                        "id": {"S": "question-002"},
                        "text": {"S": "¿Qué ingrediente es esencial en la pasta carbonara?"},
                        "category": {"S": "cooking"},
                        "type": {"S": "multiple_choice"},
                        "options": {"L": [
                            {"S": "Crema"},
                            {"S": "Huevo"},
                            {"S": "Mantequilla"},
                            {"S": "Aceite"}
                        ]},
                        "correctAnswer": {"S": "Huevo"},
                        "difficulty": {"S": "medium"},
                        "createdAt": {"S": "2024-01-02T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-02T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "QUESTION#question-003"},
                        "SK": {"S": "METADATA"},
                        "GSI1PK": {"S": "QUESTION"},
                        "GSI1SK": {"S": "question-003"},
                        "id": {"S": "question-003"},
                        "text": {"S": "¿Cuántos minutos se debe cocinar la paella?"},
                        "category": {"S": "cooking"},
                        "type": {"S": "multiple_choice"},
                        "options": {"L": [
                            {"S": "15 minutos"},
                            {"S": "18 minutos"},
                            {"S": "25 minutos"},
                            {"S": "30 minutos"}
                        ]},
                        "correctAnswer": {"S": "18 minutos"},
                        "difficulty": {"S": "hard"},
                        "createdAt": {"S": "2024-01-03T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-03T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "USER#user-123"},
                        "SK": {"S": "LIFEPLAN#plan-001"},
                        "GSI1PK": {"S": "LIFEPLAN"},
                        "GSI1SK": {"S": "plan-001"},
                        "id": {"S": "plan-001"},
                        "userId": {"S": "user-123"},
                        "title": {"S": "Mejorar habilidades culinarias"},
                        "description": {"S": "Aprender a cocinar 20 recetas nuevas este año"},
                        "category": {"S": "cooking"},
                        "targetDate": {"S": "2024-12-31"},
                        "goals": {"L": [
                            {"M": {
                                "id": {"S": "goal-001"},
                                "title": {"S": "Aprender pasta italiana"},
                                "completed": {"BOOL": true}
                            }},
                            {"M": {
                                "id": {"S": "goal-002"},
                                "title": {"S": "Dominar técnicas de paella"},
                                "completed": {"BOOL": false}
                            }}
                        ]},
                        "progress": {"N": "25"},
                        "status": {"S": "active"},
                        "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-10T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "USER#user-123"},
                        "SK": {"S": "LIFEPLAN#plan-002"},
                        "GSI1PK": {"S": "LIFEPLAN"},
                        "GSI1SK": {"S": "plan-002"},
                        "id": {"S": "plan-002"},
                        "userId": {"S": "user-123"},
                        "title": {"S": "Vida saludable"},
                        "description": {"S": "Adoptar hábitos más saludables"},
                        "category": {"S": "health"},
                        "targetDate": {"S": "2024-06-30"},
                        "goals": {"L": [
                            {"M": {
                                "id": {"S": "goal-003"},
                                "title": {"S": "Ejercicio 3 veces por semana"},
                                "completed": {"BOOL": false}
                            }}
                        ]},
                        "progress": {"N": "10"},
                        "status": {"S": "active"},
                        "createdAt": {"S": "2024-01-05T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-05T00:00:00.000Z"}
                    }
                }
            },
            {
                "PutRequest": {
                    "Item": {
                        "PK": {"S": "USER#user-123"},
                        "SK": {"S": "SCHEDULE#schedule-001"},
                        "GSI1PK": {"S": "SCHEDULE"},
                        "GSI1SK": {"S": "schedule-001"},
                        "id": {"S": "schedule-001"},
                        "userId": {"S": "user-123"},
                        "title": {"S": "Regar árboles"},
                        "description": {"S": "Recordatorio diario para regar los árboles"},
                        "type": {"S": "reminder"},
                        "scheduledTime": {"S": "2024-01-15T08:00:00.000Z"},
                        "recurrence": {"S": "daily"},
                        "action": {"M": {
                            "type": {"S": "water_tree"},
                            "data": {"M": {
                                "userId": {"S": "user-123"},
                                "treeId": {"S": "tree-001"},
                                "waterAmount": {"N": "20"}
                            }}
                        }},
                        "isActive": {"BOOL": true},
                        "nextTrigger": {"S": "2024-01-15T08:00:00.000Z"},
                        "createdAt": {"S": "2024-01-01T00:00:00.000Z"},
                        "updatedAt": {"S": "2024-01-01T00:00:00.000Z"}
                    }
                }
            }
        ]
    }
}
MOREDATA

    aws dynamodb batch-write-item --request-items file://more_data.json --region $REGION

    if [ $? -eq 0 ]; then
        echo "   ✅ Datos adicionales insertados exitosamente"
    else
        echo "   ❌ Error insertando datos adicionales"
    fi

    rm more_data.json
}

# Ejecutar funciones de población de datos
populate_test_data
populate_more_data

echo ""
echo "🎉 ¡Función hopie-scheduler-dev creada y base de datos poblada!"
echo ""
echo "📋 Función creada:"
echo "   ✅ hopie-scheduler-dev (Programación y tareas automáticas)"
echo ""
echo "📊 Datos de prueba insertados:"
echo "   ✅ 3 usuarios de prueba"
echo "   ✅ 3 recetas de ejemplo"
echo "   ✅ 3 preguntas de prueba"
echo "   ✅ 2 planes de vida"
echo "   ✅ 1 relación de pareja"
echo "   ✅ 1 programación automática"
echo ""
echo "🔗 Para verificar los datos, ejecuta:"
echo "   aws dynamodb scan --table-name $TABLE_NAME --limit 10 --region $REGION"
echo ""
echo "🚀 ¡HopieApp está completamente configurado con datos de prueba!"
