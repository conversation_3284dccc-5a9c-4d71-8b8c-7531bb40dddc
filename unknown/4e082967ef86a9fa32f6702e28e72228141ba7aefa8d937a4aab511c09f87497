param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage,
    
    [Parameter(Mandatory=$false)]
    [string]$StackName = "HopieApp-$Stage"
)

# Colores para output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput $Blue "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "❌ $message"
}

function Deploy-Phase($phaseName, $parameters) {
    Write-Info "=== FASE: $phaseName ==="
    
    $samCommand = @(
        "sam", "deploy"
        "--template-file", "template.yaml"
        "--stack-name", $StackName
        "--capabilities", "CAPABILITY_IAM", "CAPABILITY_AUTO_EXPAND"
        "--disable-rollback"
        "--parameter-overrides"
    ) + $parameters
    
    Write-Info "Ejecutando: $($samCommand -join ' ')"
    
    try {
        & $samCommand[0] $samCommand[1..($samCommand.Length-1)]
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Fase '$phaseName' completada exitosamente"
            return $true
        } else {
            Write-Error "Fase '$phaseName' falló con código: $LASTEXITCODE"
            
            # Mostrar eventos del stack
            Write-Info "Últimos eventos del stack:"
            aws cloudformation describe-stack-events --stack-name $StackName --max-items 5 --output table
            
            return $false
        }
    } catch {
        Write-Error "Error en fase '$phaseName': $_"
        return $false
    }
}

function Wait-UserConfirmation($message) {
    Write-Warning $message
    $confirmation = Read-Host "¿Continuar? (y/N)"
    return ($confirmation -eq "y" -or $confirmation -eq "Y")
}

# Verificar herramientas
Write-Info "Verificando herramientas necesarias..."
try {
    $samVersion = sam --version
    Write-Success "SAM CLI: $samVersion"
    
    $awsIdentity = aws sts get-caller-identity --output json | ConvertFrom-Json
    Write-Success "AWS Account: $($awsIdentity.Account)"
} catch {
    Write-Error "Error verificando herramientas. Asegúrate de tener SAM CLI y AWS CLI configurados."
    exit 1
}

# Construir aplicación
Write-Info "Construyendo aplicación..."
sam build
if ($LASTEXITCODE -ne 0) {
    Write-Error "Error al construir la aplicación"
    exit 1
}

Write-Success "Iniciando despliegue por fases para evitar rollbacks"
Write-Warning "Este proceso desplegará la infraestructura en fases separadas"

# FASE 1: Recursos base (DynamoDB, S3, Cognito)
Write-Info "FASE 1: Desplegando recursos base (DynamoDB, S3, Cognito)"
$phase1Params = @(
    "Stage=$Stage"
    "CognitoDomainPrefix=hopie-app-$Stage"
    "EnableLambdaFunctions=false"
    "EnableWebSocket=false"
)

if (-not (Deploy-Phase "Recursos Base" $phase1Params)) {
    Write-Error "Fase 1 falló. Deteniendo despliegue."
    exit 1
}

if (-not (Wait-UserConfirmation "Fase 1 completada. ¿Continuar con CloudFront?")) {
    Write-Warning "Despliegue detenido por el usuario"
    exit 0
}

# FASE 2: CloudFront y API Gateway
Write-Info "FASE 2: Desplegando CloudFront y API Gateway"
$phase2Params = @(
    "Stage=$Stage"
    "CognitoDomainPrefix=hopie-app-$Stage"
    "EnableLambdaFunctions=false"
    "EnableWebSocket=false"
)

if (-not (Deploy-Phase "CloudFront y API Gateway" $phase2Params)) {
    Write-Error "Fase 2 falló. Los recursos de la Fase 1 permanecen intactos."
    exit 1
}

if (-not (Wait-UserConfirmation "Fase 2 completada. ¿Continuar con funciones Lambda?")) {
    Write-Warning "Despliegue detenido por el usuario"
    exit 0
}

# FASE 3: Funciones Lambda (sin WebSocket)
Write-Info "FASE 3: Desplegando funciones Lambda"
$phase3Params = @(
    "Stage=$Stage"
    "CognitoDomainPrefix=hopie-app-$Stage"
    "EnableLambdaFunctions=true"
    "EnableWebSocket=false"
)

if (-not (Deploy-Phase "Funciones Lambda" $phase3Params)) {
    Write-Error "Fase 3 falló. Recursos anteriores permanecen intactos."
    Write-Info "Puedes intentar desplegar solo las Lambdas nuevamente con:"
    Write-Info ".\deploy.ps1 -Stage $Stage -SkipWebSocket"
    exit 1
}

if (-not (Wait-UserConfirmation "Fase 3 completada. ¿Continuar con WebSocket?")) {
    Write-Warning "Despliegue detenido por el usuario"
    exit 0
}

# FASE 4: WebSocket API
Write-Info "FASE 4: Desplegando WebSocket API"
$phase4Params = @(
    "Stage=$Stage"
    "CognitoDomainPrefix=hopie-app-$Stage"
    "EnableLambdaFunctions=true"
    "EnableWebSocket=true"
)

if (-not (Deploy-Phase "WebSocket API" $phase4Params)) {
    Write-Error "Fase 4 falló. Recursos anteriores permanecen intactos."
    Write-Info "Puedes intentar desplegar solo WebSocket nuevamente con:"
    Write-Info ".\deploy.ps1 -Stage $Stage"
    exit 1
}

Write-Success "🎉 ¡Despliegue por fases completado exitosamente!"

# Mostrar outputs finales
Write-Info "Outputs del stack:"
aws cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs" --output table

Write-Info "URLs importantes:"
$apiUrl = aws cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text
$wsUrl = aws cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs[?OutputKey=='WebSocketUrl'].OutputValue" --output text
$cfUrl = aws cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs[?OutputKey=='CloudFrontUrl'].OutputValue" --output text

Write-Success "API Gateway: $apiUrl"
Write-Success "WebSocket: $wsUrl"
Write-Success "CloudFront: $cfUrl"
