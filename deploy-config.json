{"dev": {"enableLambdaFunctions": "true", "enableWebSocket": "true", "cognitoDomainPrefix": "hopie-app-dev", "deploymentStrategy": "AllAtOnce", "rollbackConfiguration": {"rollbackTriggers": [], "monitoringTimeInMinutes": 0}}, "staging": {"enableLambdaFunctions": "true", "enableWebSocket": "true", "cognitoDomainPrefix": "hopie-app-staging", "deploymentStrategy": "Linear10PercentEvery1Minute", "rollbackConfiguration": {"rollbackTriggers": [{"arn": "arn:aws:cloudwatch:*:*:alarm:*", "type": "AWS::CloudWatch::Alarm"}], "monitoringTimeInMinutes": 5}}, "prod": {"enableLambdaFunctions": "true", "enableWebSocket": "true", "cognitoDomainPrefix": "hopie-app-prod", "deploymentStrategy": "Canary10Percent5Minutes", "rollbackConfiguration": {"rollbackTriggers": [{"arn": "arn:aws:cloudwatch:*:*:alarm:*", "type": "AWS::CloudWatch::Alarm"}], "monitoringTimeInMinutes": 10}}}