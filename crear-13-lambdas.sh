#!/bin/bash

# 🚀 Script para crear las 13 funciones Lambda restantes de HopieApp
# (Excluyendo hopie-auth-dev que ya existe)

TABLE_NAME="hopie-main-table-dev"
USER_POOL_ID="us-east-2_gTZjXep7j"
CLIENT_ID="63604alcg1rvili3ksfan61fid"
REGION="us-east-2"
BUCKET_NAME="hopie-app-assets-dev-123456"
ROLE_ARN="arn:aws:iam::864899858226:role/hopie-lambda-execution-role"

echo "🚀 Creando las 13 funciones Lambda restantes para HopieApp..."
echo "⚠️  NOTA: hopie-auth-dev ya existe, se omite"
echo ""

# Función para crear Lambda
create_lambda() {
    local FUNCTION_NAME=$1
    local DESCRIPTION=$2
    local ENV_VARS=$3
    
    echo "📦 Creando función: $FUNCTION_NAME"
    
    # Crear archivo ZIP temporal
    cd lambda-functions/$FUNCTION_NAME
    zip -r ../../${FUNCTION_NAME}.zip . > /dev/null 2>&1
    cd ../..
    
    # Crear función Lambda
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://${FUNCTION_NAME}.zip \
        --description "$DESCRIPTION" \
        --timeout 30 \
        --memory-size 256 \
        --environment Variables="$ENV_VARS" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $FUNCTION_NAME creada exitosamente"
    else
        echo "   ❌ Error creando $FUNCTION_NAME"
    fi
    
    # Limpiar archivo ZIP
    rm ${FUNCTION_NAME}.zip
}

# Crear directorio para funciones
mkdir -p lambda-functions

# 1. UserFunction
mkdir -p lambda-functions/hopie-user-dev
cat > lambda-functions/hopie-user-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/profile')) {
            return await getUserProfile(event);
        } else if (httpMethod === 'PUT' && path.includes('/profile')) {
            return await updateUserProfile(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUserProfile(event) {
    return {
        statusCode: 200,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ user: { id: 'user-123', name: 'Usuario Demo' } })
    };
}

async function updateUserProfile(body, event) {
    return {
        statusCode: 200,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ message: 'Profile updated successfully' })
    };
}
EOF

# 2. CoupleFunction
mkdir -p lambda-functions/hopie-couple-dev
cat > lambda-functions/hopie-couple-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'POST' && path.includes('/create')) {
            return await createCouple(requestBody);
        } else if (httpMethod === 'GET' && path.includes('/info')) {
            return await getCoupleInfo();
        }
        
        return {
            statusCode: 404,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function createCouple(body) {
    return {
        statusCode: 201,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ message: 'Couple created successfully' })
    };
}

async function getCoupleInfo() {
    return {
        statusCode: 200,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ couple: { id: 'couple-123', status: 'active' } })
    };
}
EOF

# 3. TreeFunction
mkdir -p lambda-functions/hopie-tree-dev
cat > lambda-functions/hopie-tree-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/family')) {
            return await getFamilyTree();
        } else if (httpMethod === 'POST' && path.includes('/add-member')) {
            return await addFamilyMember(requestBody);
        }
        
        return {
            statusCode: 404,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getFamilyTree() {
    return {
        statusCode: 200,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ familyMembers: [] })
    };
}

async function addFamilyMember(body) {
    return {
        statusCode: 201,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ message: 'Family member added successfully' })
    };
}
EOF

# 4. QuestionsFunction
mkdir -p lambda-functions/hopie-questions-dev
cat > lambda-functions/hopie-questions-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/daily')) {
            return await getDailyQuestion();
        } else if (httpMethod === 'POST' && path.includes('/answer')) {
            return await submitAnswer(requestBody);
        }
        
        return {
            statusCode: 404,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getDailyQuestion() {
    const questions = [
        "¿Cuál es tu recuerdo favorito de nuestra relación?",
        "¿Qué es lo que más admiras de tu pareja?",
        "¿Cuál sería tu cita ideal?"
    ];
    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
    
    return {
        statusCode: 200,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ question: { text: randomQuestion, id: Date.now() } })
    };
}

async function submitAnswer(body) {
    return {
        statusCode: 201,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ message: 'Answer submitted successfully' })
    };
}
EOF

# 5. LifePlanFunction
mkdir -p lambda-functions/hopie-lifeplan-dev
cat > lambda-functions/hopie-lifeplan-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/goals')) {
            return await getLifeGoals();
        } else if (httpMethod === 'POST' && path.includes('/create')) {
            return await createLifeGoal(requestBody);
        }
        
        return {
            statusCode: 404,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers: { 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getLifeGoals() {
    return {
        statusCode: 200,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ goals: [] })
    };
}

async function createLifeGoal(body) {
    return {
        statusCode: 201,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({ message: 'Life goal created successfully' })
    };
}
EOF

# Crear las primeras 5 funciones
echo "📁 Creando funciones 1-5..."
create_lambda "hopie-user-dev" "HopieApp User Management Function" "{TABLE_NAME=$TABLE_NAME}"
create_lambda "hopie-couple-dev" "HopieApp Couple Management Function" "{TABLE_NAME=$TABLE_NAME}"
create_lambda "hopie-tree-dev" "HopieApp Family Tree Function" "{TABLE_NAME=$TABLE_NAME}"
create_lambda "hopie-questions-dev" "HopieApp Questions Function" "{TABLE_NAME=$TABLE_NAME}"
create_lambda "hopie-lifeplan-dev" "HopieApp Life Plan Function" "{TABLE_NAME=$TABLE_NAME}"

echo ""
echo "🎉 ¡Primeras 5 funciones creadas!"
echo "📋 Funciones creadas:"
echo "   ✅ hopie-user-dev"
echo "   ✅ hopie-couple-dev"
echo "   ✅ hopie-tree-dev"
echo "   ✅ hopie-questions-dev"
echo "   ✅ hopie-lifeplan-dev"
echo ""
echo "🔗 Para verificar:"
echo "   aws lambda list-functions --query 'Functions[?starts_with(FunctionName, \`hopie-\`)].FunctionName' --output table"
