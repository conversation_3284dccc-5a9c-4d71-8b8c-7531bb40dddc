const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const apigatewaymanagementapi = new AWS.ApiGatewayManagementApi({
    endpoint: process.env.WEBSOCKET_ENDPOINT
});

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { requestContext, body } = event;
    const { routeKey, connectionId } = requestContext;
    
    try {
        switch (routeKey) {
            case '$connect':
                return await handleConnect(connectionId, event);
            case '$disconnect':
                return await handleDisconnect(connectionId, event);
            case 'sendMessage':
                return await handleSendMessage(connectionId, JSON.parse(body || '{}'), event);
            case 'joinRoom':
                return await handleJoinRoom(connectionId, JSON.parse(body || '{}'), event);
            case 'leaveRoom':
                return await handleLeaveRoom(connectionId, JSON.parse(body || '{}'), event);
            case 'typing':
                return await handleTyping(connectionId, JSON.parse(body || '{}'), event);
            default:
                return { statusCode: 400, body: 'Unknown route' };
        }
    } catch (error) {
        console.error('Error:', error);
        return { statusCode: 500, body: 'Internal server error' };
    }
};

async function handleConnect(connectionId, event) {
    const userId = event.queryStringParameters?.userId || 'anonymous';
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA',
            connectionId,
            userId,
            connectedAt: new Date().toISOString(),
            status: 'connected'
        }
    };
    
    await dynamodb.put(params).promise();
    
    console.log(`User ${userId} connected with connection ${connectionId}`);
    return { statusCode: 200 };
}

async function handleDisconnect(connectionId, event) {
    // Remove connection from database
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    await dynamodb.delete(params).promise();
    
    console.log(`Connection ${connectionId} disconnected`);
    return { statusCode: 200 };
}

async function handleSendMessage(connectionId, data, event) {
    const { roomId, message, messageType } = data;
    const messageId = `msg-${Date.now()}`;
    
    // Get connection info to get userId
    const connectionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    const connectionResult = await dynamodb.get(connectionParams).promise();
    const userId = connectionResult.Item?.userId || 'anonymous';
    
    // Save message to database
    const messageParams = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ROOM#${roomId}`,
            SK: `MESSAGE#${messageId}`,
            messageId,
            userId,
            message,
            messageType: messageType || 'text',
            timestamp: new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(messageParams).promise();
    
    // Get all connections in the room
    const roomConnectionsParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ROOM#${roomId}`,
            ':sk': 'CONNECTION#'
        }
    };
    
    const roomConnections = await dynamodb.query(roomConnectionsParams).promise();
    
    // Send message to all connections in the room
    const messageData = {
        type: 'message',
        messageId,
        roomId,
        userId,
        message,
        messageType,
        timestamp: new Date().toISOString()
    };
    
    const sendPromises = (roomConnections.Items || []).map(async (connection) => {
        try {
            await apigatewaymanagementapi.postToConnection({
                ConnectionId: connection.connectionId,
                Data: JSON.stringify(messageData)
            }).promise();
        } catch (error) {
            if (error.statusCode === 410) {
                // Connection is stale, remove it
                await dynamodb.delete({
                    TableName: TABLE_NAME,
                    Key: {
                        PK: `ROOM#${roomId}`,
                        SK: `CONNECTION#${connection.connectionId}`
                    }
                }).promise();
            }
        }
    });
    
    await Promise.all(sendPromises);
    
    return { statusCode: 200 };
}

async function handleJoinRoom(connectionId, data, event) {
    const { roomId } = data;
    
    // Get connection info
    const connectionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    const connectionResult = await dynamodb.get(connectionParams).promise();
    const userId = connectionResult.Item?.userId || 'anonymous';
    
    // Add connection to room
    const roomConnectionParams = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ROOM#${roomId}`,
            SK: `CONNECTION#${connectionId}`,
            connectionId,
            userId,
            joinedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(roomConnectionParams).promise();
    
    // Notify other users in the room
    const notificationData = {
        type: 'userJoined',
        roomId,
        userId,
        timestamp: new Date().toISOString()
    };
    
    await sendToConnection(connectionId, JSON.stringify({
        type: 'joinedRoom',
        roomId,
        message: 'Successfully joined room'
    }));
    
    console.log(`User ${userId} joined room ${roomId}`);
    return { statusCode: 200 };
}

async function handleLeaveRoom(connectionId, data, event) {
    const { roomId } = data;
    
    // Remove connection from room
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `ROOM#${roomId}`,
            SK: `CONNECTION#${connectionId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    console.log(`Connection ${connectionId} left room ${roomId}`);
    return { statusCode: 200 };
}

async function handleTyping(connectionId, data, event) {
    const { roomId, isTyping } = data;
    
    // Get connection info
    const connectionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CONNECTION#${connectionId}`,
            SK: 'METADATA'
        }
    };
    
    const connectionResult = await dynamodb.get(connectionParams).promise();
    const userId = connectionResult.Item?.userId || 'anonymous';
    
    // Get all other connections in the room
    const roomConnectionsParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        FilterExpression: 'connectionId <> :currentConnection',
        ExpressionAttributeValues: {
            ':pk': `ROOM#${roomId}`,
            ':sk': 'CONNECTION#',
            ':currentConnection': connectionId
        }
    };
    
    const roomConnections = await dynamodb.query(roomConnectionsParams).promise();
    
    // Send typing indicator to other users
    const typingData = {
        type: 'typing',
        roomId,
        userId,
        isTyping,
        timestamp: new Date().toISOString()
    };
    
    const sendPromises = (roomConnections.Items || []).map(async (connection) => {
        try {
            await apigatewaymanagementapi.postToConnection({
                ConnectionId: connection.connectionId,
                Data: JSON.stringify(typingData)
            }).promise();
        } catch (error) {
            if (error.statusCode === 410) {
                // Connection is stale, remove it
                await dynamodb.delete({
                    TableName: TABLE_NAME,
                    Key: {
                        PK: `ROOM#${roomId}`,
                        SK: `CONNECTION#${connection.connectionId}`
                    }
                }).promise();
            }
        }
    });
    
    await Promise.all(sendPromises);
    
    return { statusCode: 200 };
}

async function sendToConnection(connectionId, data) {
    try {
        await apigatewaymanagementapi.postToConnection({
            ConnectionId: connectionId,
            Data: data
        }).promise();
    } catch (error) {
        console.error('Error sending to connection:', error);
    }
}
