const AWS = require('aws-sdk');
const sharp = require('sharp');

/**
 * Image Processor Service - Handle automatic image processing on S3 uploads
 */

const s3 = new AWS.S3();

exports.handler = async(event, context) => {
  console.log('Image processor event:', JSON.stringify(event, null, 2));

  try {
    for (const record of event.Records) {
      if (record.eventName.startsWith('ObjectCreated')) {
        await processImage(record);
      }
    }

    return { statusCode: 200, body: 'Images processed successfully' };
  } catch (error) {
    console.error('Image processor error:', error);
    return { statusCode: 500, body: 'Image processing failed' };
  }
};

async function processImage(record) {
  try {
    const bucket = record.s3.bucket.name;
    const key = decodeURIComponent(record.s3.object.key.replace(/\+/g, ' '));

    console.log(`Processing image: ${bucket}/${key}`);

    // Skip if it's already a thumbnail
    if (key.includes('_thumb.')) {
      return;
    }

    // Get the image from S3
    const imageObject = await s3.getObject({
      Bucket: bucket,
      Key: key
    }).promise();

    // Create thumbnail
    const thumbnailBuffer = await sharp(imageObject.Body)
      .resize(300, 300, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 80 })
      .toBuffer();

    // Generate thumbnail key
    const pathParts = key.split('/');
    const fileName = pathParts.pop();
    const fileNameParts = fileName.split('.');
    const extension = fileNameParts.pop();
    const baseName = fileNameParts.join('.');
    const thumbnailKey = [...pathParts, `${baseName}_thumb.${extension}`].join('/');

    // Upload thumbnail
    await s3.putObject({
      Bucket: bucket,
      Key: thumbnailKey,
      Body: thumbnailBuffer,
      ContentType: 'image/jpeg'
    }).promise();

    console.log(`Thumbnail created: ${thumbnailKey}`);

    // Optimize original image if it's too large
    const metadata = await sharp(imageObject.Body).metadata();

    if (metadata.width > 1920 || metadata.height > 1920) {
      const optimizedBuffer = await sharp(imageObject.Body)
        .resize(1920, 1920, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({ quality: 85 })
        .toBuffer();

      // Replace original with optimized version
      await s3.putObject({
        Bucket: bucket,
        Key: key,
        Body: optimizedBuffer,
        ContentType: 'image/jpeg'
      }).promise();

      console.log(`Image optimized: ${key}`);
    }

  } catch (error) {
    console.error('Process image error:', error);
    throw error;
  }
}
