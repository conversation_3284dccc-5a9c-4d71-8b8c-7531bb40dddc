#!/bin/bash

# 🚀 Script para crear las 4 funciones Lambda restantes de HopieApp
# Continuación de las funciones principales

TABLE_NAME="hopie-main-table-dev"
USER_POOL_ID="us-east-2_gTZjXep7j"
CLIENT_ID="63604alcg1rvili3ksfan61fid"
REGION="us-east-2"
BUCKET_NAME="hopie-app-assets-dev-123456"
ROLE_ARN="arn:aws:iam::864899858226:role/hopie-lambda-execution-role"

echo "🚀 Creando las 4 funciones Lambda restantes para HopieApp..."
echo "📋 Variables configuradas:"
echo "   TABLE_NAME: $TABLE_NAME"
echo "   USER_POOL_ID: $USER_POOL_ID"
echo "   CLIENT_ID: $CLIENT_ID"
echo "   REGION: $REGION"
echo "   BUCKET_NAME: $BUCKET_NAME"
echo "   ROLE_ARN: $ROLE_ARN"
echo ""

# Función para crear Lambda
create_lambda() {
    local FUNCTION_NAME=$1
    local DESCRIPTION=$2
    local ENV_VARS=$3
    
    echo "📦 Creando función: $FUNCTION_NAME"
    
    # Crear archivo ZIP temporal
    cd lambda-functions/$FUNCTION_NAME
    zip -r ../../${FUNCTION_NAME}.zip . > /dev/null 2>&1
    cd ../..
    
    # Crear función Lambda
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://${FUNCTION_NAME}.zip \
        --description "$DESCRIPTION" \
        --timeout 30 \
        --memory-size 256 \
        --environment Variables="$ENV_VARS" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $FUNCTION_NAME creada exitosamente"
    else
        echo "   ❌ Error creando $FUNCTION_NAME"
    fi
    
    # Limpiar archivo ZIP
    rm ${FUNCTION_NAME}.zip
}

# Crear directorio para funciones
mkdir -p lambda-functions

# 4. LifePlanFunction
mkdir -p lambda-functions/hopie-lifeplan-dev
cat > lambda-functions/hopie-lifeplan-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/lifeplans')) {
            return await getLifePlans(event);
        } else if (httpMethod === 'POST' && path.includes('/lifeplans')) {
            return await createLifePlan(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/lifeplan/')) {
            return await getLifePlanById(event);
        } else if (httpMethod === 'PUT' && path.includes('/lifeplan/')) {
            return await updateLifePlan(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/lifeplan/')) {
            return await deleteLifePlan(event);
        } else if (httpMethod === 'POST' && path.includes('/lifeplan/goal')) {
            return await addGoal(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/lifeplan/goal/')) {
            return await updateGoal(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getLifePlans(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LIFEPLAN#'
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            lifePlans: result.Items || []
        })
    };
}

async function createLifePlan(body, event) {
    const userId = 'user-123';
    const planId = `lifeplan-${Date.now()}`;
    const { title, description, category, targetDate, goals } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`,
            GSI1PK: 'LIFEPLAN',
            GSI1SK: planId,
            id: planId,
            userId,
            title,
            description,
            category,
            targetDate,
            goals: goals || [],
            progress: 0,
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life plan created successfully',
            lifePlan: params.Item
        })
    };
}

async function getLifePlanById(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'lifeplan-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            lifePlan: result.Item || null
        })
    };
}

async function updateLifePlan(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'lifeplan-123';
    const { title, description, category, targetDate, progress, status } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        },
        UpdateExpression: 'SET title = :title, description = :description, category = :category, targetDate = :targetDate, progress = :progress, #status = :status, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#status': 'status'
        },
        ExpressionAttributeValues: {
            ':title': title,
            ':description': description,
            ':category': category,
            ':targetDate': targetDate,
            ':progress': progress,
            ':status': status,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life plan updated successfully',
            lifePlan: result.Attributes
        })
    };
}

async function deleteLifePlan(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'lifeplan-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life plan deleted successfully'
        })
    };
}

async function addGoal(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.planId || body.planId;
    const goalId = `goal-${Date.now()}`;
    const { title, description, targetDate, priority } = body;
    
    const goal = {
        id: goalId,
        title,
        description,
        targetDate,
        priority: priority || 'medium',
        completed: false,
        createdAt: new Date().toISOString()
    };
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        },
        UpdateExpression: 'SET goals = list_append(if_not_exists(goals, :empty_list), :goal), updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':goal': [goal],
            ':empty_list': [],
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Goal added successfully',
            goal,
            lifePlan: result.Attributes
        })
    };
}

async function updateGoal(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.planId || body.planId;
    const goalId = event.pathParameters?.goalId || body.goalId;
    const { completed } = body;
    
    // This is a simplified version - in a real implementation, you'd need to
    // update the specific goal in the goals array
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        },
        UpdateExpression: 'SET updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Goal updated successfully',
            lifePlan: result.Attributes
        })
    };
}
EOF

# 5. PlacesFunction
mkdir -p lambda-functions/hopie-places-dev
cat > lambda-functions/hopie-places-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/places')) {
            return await getPlaces(event);
        } else if (httpMethod === 'POST' && path.includes('/places')) {
            return await createPlace(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/place/')) {
            return await getPlaceById(event);
        } else if (httpMethod === 'PUT' && path.includes('/place/')) {
            return await updatePlace(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/place/')) {
            return await deletePlace(event);
        } else if (httpMethod === 'GET' && path.includes('/places/nearby')) {
            return await getNearbyPlaces(event);
        } else if (httpMethod === 'POST' && path.includes('/place/visit')) {
            return await markAsVisited(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getPlaces(event) {
    const category = event.queryStringParameters?.category || 'all';

    let params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'PLACE'
        }
    };

    if (category !== 'all') {
        params.FilterExpression = 'category = :category';
        params.ExpressionAttributeValues[':category'] = category;
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            places: result.Items || []
        })
    };
}

async function createPlace(body, event) {
    const placeId = `place-${Date.now()}`;
    const { name, description, category, address, coordinates, website, phone } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA',
            GSI1PK: 'PLACE',
            GSI1SK: placeId,
            id: placeId,
            name,
            description,
            category,
            address,
            coordinates: coordinates || { lat: 0, lng: 0 },
            website,
            phone,
            rating: 0,
            reviewCount: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place created successfully',
            place: params.Item
        })
    };
}

async function getPlaceById(event) {
    const placeId = event.pathParameters?.id || 'place-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            place: result.Item || null
        })
    };
}

async function updatePlace(body, event) {
    const placeId = event.pathParameters?.id || 'place-123';
    const { name, description, category, address, coordinates, website, phone } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #name = :name, description = :description, category = :category, address = :address, coordinates = :coordinates, website = :website, phone = :phone, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':description': description,
            ':category': category,
            ':address': address,
            ':coordinates': coordinates,
            ':website': website,
            ':phone': phone,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place updated successfully',
            place: result.Attributes
        })
    };
}

async function deletePlace(event) {
    const placeId = event.pathParameters?.id || 'place-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place deleted successfully'
        })
    };
}

async function getNearbyPlaces(event) {
    const lat = parseFloat(event.queryStringParameters?.lat || '0');
    const lng = parseFloat(event.queryStringParameters?.lng || '0');
    const radius = parseFloat(event.queryStringParameters?.radius || '10');

    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'PLACE'
        }
    };

    const result = await dynamodb.query(params).promise();

    const nearbyPlaces = (result.Items || []).filter(place => {
        if (!place.coordinates) return false;
        const distance = calculateDistance(lat, lng, place.coordinates.lat, place.coordinates.lng);
        return distance <= radius;
    });

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            places: nearbyPlaces,
            searchCenter: { lat, lng },
            radius
        })
    };
}

async function markAsVisited(body, event) {
    const userId = 'user-123';
    const { placeId, visitDate, notes } = body;
    const visitId = `visit-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `VISIT#${visitId}`,
            id: visitId,
            userId,
            placeId,
            visitDate: visitDate || new Date().toISOString(),
            notes,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place marked as visited successfully',
            visit: params.Item
        })
    };
}

function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371;
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}
EOF

# 6. LocationFunction
mkdir -p lambda-functions/hopie-location-dev
cat > lambda-functions/hopie-location-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/location/update')) {
            return await updateLocation(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/location/current')) {
            return await getCurrentLocation(event);
        } else if (httpMethod === 'GET' && path.includes('/location/history')) {
            return await getLocationHistory(event);
        } else if (httpMethod === 'POST' && path.includes('/location/share')) {
            return await shareLocation(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/location/shared')) {
            return await getSharedLocations(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function updateLocation(body, event) {
    const userId = 'user-123';
    const { latitude, longitude, accuracy, timestamp } = body;
    const locationId = `location-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `LOCATION#${locationId}`,
            id: locationId,
            userId,
            latitude,
            longitude,
            accuracy,
            timestamp: timestamp || new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    // Also update current location
    const currentParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'CURRENT_LOCATION'
        },
        UpdateExpression: 'SET latitude = :latitude, longitude = :longitude, accuracy = :accuracy, #timestamp = :timestamp, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#timestamp': 'timestamp'
        },
        ExpressionAttributeValues: {
            ':latitude': latitude,
            ':longitude': longitude,
            ':accuracy': accuracy,
            ':timestamp': timestamp || new Date().toISOString(),
            ':updatedAt': new Date().toISOString()
        }
    };

    await dynamodb.update(currentParams).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Location updated successfully',
            location: params.Item
        })
    };
}

async function getCurrentLocation(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'CURRENT_LOCATION'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            currentLocation: result.Item || null
        })
    };
}

async function getLocationHistory(event) {
    const userId = 'user-123';
    const limit = parseInt(event.queryStringParameters?.limit || '50');

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LOCATION#'
        },
        ScanIndexForward: false,
        Limit: limit
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            locationHistory: result.Items || []
        })
    };
}

async function shareLocation(body, event) {
    const userId = 'user-123';
    const { shareWithUserId, duration, message } = body;
    const shareId = `share-${Date.now()}`;

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + (duration || 1)); // Default 1 hour

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `LOCATION_SHARE#${shareId}`,
            id: shareId,
            userId,
            shareWithUserId,
            message,
            expiresAt: expiresAt.toISOString(),
            active: true,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Location sharing enabled successfully',
            share: params.Item
        })
    };
}

async function getSharedLocations(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LOCATION_SHARE#'
        },
        FilterExpression: 'active = :active AND expiresAt > :now',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LOCATION_SHARE#',
            ':active': true,
            ':now': new Date().toISOString()
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            sharedLocations: result.Items || []
        })
    };
}
EOF

# 7. StatsFunction
mkdir -p lambda-functions/hopie-stats-dev
cat > lambda-functions/hopie-stats-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/stats/user')) {
            return await getUserStats(event);
        } else if (httpMethod === 'GET' && path.includes('/stats/couple')) {
            return await getCoupleStats(event);
        } else if (httpMethod === 'GET' && path.includes('/stats/activities')) {
            return await getActivityStats(event);
        } else if (httpMethod === 'GET' && path.includes('/stats/progress')) {
            return await getProgressStats(event);
        } else if (httpMethod === 'POST' && path.includes('/stats/track')) {
            return await trackActivity(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUserStats(event) {
    const userId = 'user-123';

    // Get various user statistics
    const promises = [
        // Get recipes count
        dynamodb.query({
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk',
            ExpressionAttributeValues: { ':gsi1pk': 'RECIPE' }
        }).promise(),

        // Get favorites count
        dynamodb.query({
            TableName: TABLE_NAME,
            KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
            ExpressionAttributeValues: { ':pk': `USER#${userId}`, ':sk': 'FAVORITE#' }
        }).promise(),

        // Get trees count
        dynamodb.query({
            TableName: TABLE_NAME,
            KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
            ExpressionAttributeValues: { ':pk': `USER#${userId}`, ':sk': 'TREE#' }
        }).promise(),

        // Get life plans count
        dynamodb.query({
            TableName: TABLE_NAME,
            KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
            ExpressionAttributeValues: { ':pk': `USER#${userId}`, ':sk': 'LIFEPLAN#' }
        }).promise()
    ];

    const [recipesResult, favoritesResult, treesResult, lifePlansResult] = await Promise.all(promises);

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            userStats: {
                recipesCount: recipesResult.Items?.length || 0,
                favoritesCount: favoritesResult.Items?.length || 0,
                treesCount: treesResult.Items?.length || 0,
                lifePlansCount: lifePlansResult.Items?.length || 0,
                joinDate: '2024-01-01',
                lastActivity: new Date().toISOString()
            }
        })
    };
}

async function getCoupleStats(event) {
    const userId = 'user-123';

    // Get couple-related statistics
    const coupleParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'COUPLE#'
        }
    };

    const coupleResult = await dynamodb.query(coupleParams).promise();
    const activeCouples = (coupleResult.Items || []).filter(couple => couple.status === 'active');

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            coupleStats: {
                activeCouples: activeCouples.length,
                totalCouples: coupleResult.Items?.length || 0,
                relationshipDuration: activeCouples.length > 0 ?
                    Math.floor((new Date() - new Date(activeCouples[0].startDate)) / (1000 * 60 * 60 * 24)) : 0
            }
        })
    };
}

async function getActivityStats(event) {
    const userId = 'user-123';
    const timeframe = event.queryStringParameters?.timeframe || 'week'; // week, month, year

    // Calculate date range based on timeframe
    const now = new Date();
    const startDate = new Date();

    switch (timeframe) {
        case 'week':
            startDate.setDate(now.getDate() - 7);
            break;
        case 'month':
            startDate.setMonth(now.getMonth() - 1);
            break;
        case 'year':
            startDate.setFullYear(now.getFullYear() - 1);
            break;
    }

    // Get activities in the timeframe
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ANALYTICS#${userId}`,
            ':sk': 'EVENT#'
        },
        FilterExpression: '#timestamp >= :startDate',
        ExpressionAttributeNames: {
            '#timestamp': 'timestamp'
        },
        ExpressionAttributeValues: {
            ':pk': `ANALYTICS#${userId}`,
            ':sk': 'EVENT#',
            ':startDate': startDate.toISOString()
        }
    };

    const result = await dynamodb.query(params).promise();
    const activities = result.Items || [];

    // Group activities by type
    const activityCounts = {};
    activities.forEach(activity => {
        activityCounts[activity.eventType] = (activityCounts[activity.eventType] || 0) + 1;
    });

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            activityStats: {
                timeframe,
                totalActivities: activities.length,
                activityCounts,
                dailyAverage: Math.round(activities.length / 7) // Simplified calculation
            }
        })
    };
}

async function getProgressStats(event) {
    const userId = 'user-123';

    // Get progress on life plans
    const lifePlansParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LIFEPLAN#'
        }
    };

    const lifePlansResult = await dynamodb.query(lifePlansParams).promise();
    const lifePlans = lifePlansResult.Items || [];

    const totalProgress = lifePlans.reduce((sum, plan) => sum + (plan.progress || 0), 0);
    const averageProgress = lifePlans.length > 0 ? totalProgress / lifePlans.length : 0;

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            progressStats: {
                totalLifePlans: lifePlans.length,
                averageProgress: Math.round(averageProgress),
                completedPlans: lifePlans.filter(plan => plan.progress >= 100).length,
                activePlans: lifePlans.filter(plan => plan.status === 'active').length
            }
        })
    };
}

async function trackActivity(body, event) {
    const userId = 'user-123';
    const { activityType, value, metadata } = body;
    const activityId = `activity-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ANALYTICS#${userId}`,
            SK: `EVENT#${activityId}`,
            id: activityId,
            userId,
            eventType: activityType,
            eventData: { value, metadata },
            timestamp: new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Activity tracked successfully',
            activity: params.Item
        })
    };
}
EOF

echo "📁 Creando las 4 funciones Lambda restantes..."
echo ""

# Crear las funciones Lambda
echo "🔄 Creando hopie-lifeplan-dev..."
create_lambda "hopie-lifeplan-dev" "HopieApp Life Plan Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-places-dev..."
create_lambda "hopie-places-dev" "HopieApp Places Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-location-dev..."
create_lambda "hopie-location-dev" "HopieApp Location Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-stats-dev..."
create_lambda "hopie-stats-dev" "HopieApp Statistics Function" "{TABLE_NAME=$TABLE_NAME}"

echo ""
echo "🎉 ¡Las 4 funciones Lambda restantes han sido creadas!"
echo ""
echo "📋 Funciones creadas:"
echo "   ✅ hopie-lifeplan-dev"
echo "   ✅ hopie-places-dev"
echo "   ✅ hopie-location-dev"
echo "   ✅ hopie-stats-dev"
echo ""
echo "🔗 Para verificar todas las funciones principales, ejecuta:"
echo "   aws lambda list-functions --query 'Functions[?starts_with(FunctionName, \`hopie-\`)].FunctionName' --output table"
echo ""
echo "🚀 ¡Todas las funciones principales de HopieApp están listas!"
