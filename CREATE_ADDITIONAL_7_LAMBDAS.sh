#!/bin/bash

# 🚀 Script para crear las 7 funciones Lambda principales adicionales de HopieApp
# Funciones específicas del dominio de la aplicación

TABLE_NAME="hopie-main-table-dev"
USER_POOL_ID="us-east-2_gTZjXep7j"
CLIENT_ID="63604alcg1rvili3ksfan61fid"
REGION="us-east-2"
BUCKET_NAME="hopie-app-assets-dev-123456"
ROLE_ARN="arn:aws:iam::864899858226:role/hopie-lambda-execution-role"

echo "🚀 Creando las 7 funciones Lambda principales adicionales para HopieApp..."
echo "📋 Variables configuradas:"
echo "   TABLE_NAME: $TABLE_NAME"
echo "   USER_POOL_ID: $USER_POOL_ID"
echo "   CLIENT_ID: $CLIENT_ID"
echo "   REGION: $REGION"
echo "   BUCKET_NAME: $BUCKET_NAME"
echo "   ROLE_ARN: $ROLE_ARN"
echo ""

# Función para crear Lambda
create_lambda() {
    local FUNCTION_NAME=$1
    local DESCRIPTION=$2
    local ENV_VARS=$3
    
    echo "📦 Creando función: $FUNCTION_NAME"
    
    # Crear archivo ZIP temporal
    cd lambda-functions/$FUNCTION_NAME
    zip -r ../../${FUNCTION_NAME}.zip . > /dev/null 2>&1
    cd ../..
    
    # Crear función Lambda
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://${FUNCTION_NAME}.zip \
        --description "$DESCRIPTION" \
        --timeout 30 \
        --memory-size 256 \
        --environment Variables="$ENV_VARS" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $FUNCTION_NAME creada exitosamente"
    else
        echo "   ❌ Error creando $FUNCTION_NAME"
    fi
    
    # Limpiar archivo ZIP
    rm ${FUNCTION_NAME}.zip
}

# Crear directorio para funciones
mkdir -p lambda-functions

# 1. CoupleFunction
mkdir -p lambda-functions/hopie-couple-dev
cat > lambda-functions/hopie-couple-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/couples')) {
            return await getCouples(event);
        } else if (httpMethod === 'POST' && path.includes('/couples')) {
            return await createCouple(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/couple/')) {
            return await getCoupleById(event);
        } else if (httpMethod === 'PUT' && path.includes('/couple/')) {
            return await updateCouple(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/couple/')) {
            return await deleteCouple(event);
        } else if (httpMethod === 'POST' && path.includes('/couple/invite')) {
            return await invitePartner(requestBody, event);
        } else if (httpMethod === 'POST' && path.includes('/couple/accept')) {
            return await acceptInvitation(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getCouples(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'COUPLE#'
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            couples: result.Items || []
        })
    };
}

async function createCouple(body, event) {
    const userId = 'user-123';
    const coupleId = `couple-${Date.now()}`;
    const { partnerEmail, relationshipType, startDate } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`,
            GSI1PK: 'COUPLE',
            GSI1SK: coupleId,
            id: coupleId,
            userId,
            partnerEmail,
            relationshipType,
            startDate,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple relationship created successfully',
            couple: params.Item
        })
    };
}

async function getCoupleById(event) {
    const userId = 'user-123';
    const coupleId = event.pathParameters?.id || 'couple-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            couple: result.Item || null
        })
    };
}

async function updateCouple(body, event) {
    const userId = 'user-123';
    const coupleId = event.pathParameters?.id || 'couple-123';
    const { relationshipType, startDate, status } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`
        },
        UpdateExpression: 'SET relationshipType = :relationshipType, startDate = :startDate, #status = :status, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#status': 'status'
        },
        ExpressionAttributeValues: {
            ':relationshipType': relationshipType,
            ':startDate': startDate,
            ':status': status,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple relationship updated successfully',
            couple: result.Attributes
        })
    };
}

async function deleteCouple(event) {
    const userId = 'user-123';
    const coupleId = event.pathParameters?.id || 'couple-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple relationship deleted successfully'
        })
    };
}

async function invitePartner(body, event) {
    const userId = 'user-123';
    const { partnerEmail, message } = body;
    
    // Logic to send invitation email would go here
    // For now, just return success
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Partner invitation sent successfully',
            invitedEmail: partnerEmail
        })
    };
}

async function acceptInvitation(body, event) {
    const userId = 'user-123';
    const { coupleId, accept } = body;
    
    if (accept) {
        const params = {
            TableName: TABLE_NAME,
            Key: {
                PK: `USER#${userId}`,
                SK: `COUPLE#${coupleId}`
            },
            UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'active',
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        };
        
        const result = await dynamodb.update(params).promise();
        
        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                message: 'Invitation accepted successfully',
                couple: result.Attributes
            })
        };
    } else {
        // Delete the invitation
        const params = {
            TableName: TABLE_NAME,
            Key: {
                PK: `USER#${userId}`,
                SK: `COUPLE#${coupleId}`
            }
        };
        
        await dynamodb.delete(params).promise();
        
        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                message: 'Invitation declined successfully'
            })
        };
    }
}
EOF

# 2. TreeFunction
mkdir -p lambda-functions/hopie-tree-dev
cat > lambda-functions/hopie-tree-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/trees')) {
            return await getTrees(event);
        } else if (httpMethod === 'POST' && path.includes('/trees')) {
            return await createTree(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/tree/')) {
            return await getTreeById(event);
        } else if (httpMethod === 'PUT' && path.includes('/tree/')) {
            return await updateTree(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/tree/')) {
            return await deleteTree(event);
        } else if (httpMethod === 'POST' && path.includes('/tree/water')) {
            return await waterTree(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/tree/growth')) {
            return await getTreeGrowth(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getTrees(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'TREE#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            trees: result.Items || []
        })
    };
}

async function createTree(body, event) {
    const userId = 'user-123';
    const treeId = `tree-${Date.now()}`;
    const { name, type, location, plantedDate } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`,
            GSI1PK: 'TREE',
            GSI1SK: treeId,
            id: treeId,
            userId,
            name,
            type,
            location,
            plantedDate,
            growthLevel: 1,
            waterLevel: 100,
            health: 100,
            lastWatered: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree created successfully',
            tree: params.Item
        })
    };
}

async function getTreeById(event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || 'tree-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            tree: result.Item || null
        })
    };
}

async function updateTree(body, event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || 'tree-123';
    const { name, type, location } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        },
        UpdateExpression: 'SET #name = :name, #type = :type, #location = :location, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name',
            '#type': 'type',
            '#location': 'location'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':type': type,
            ':location': location,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree updated successfully',
            tree: result.Attributes
        })
    };
}

async function deleteTree(event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || 'tree-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree deleted successfully'
        })
    };
}

async function waterTree(body, event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || body.treeId || 'tree-123';
    const waterAmount = body.waterAmount || 20;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        },
        UpdateExpression: 'SET waterLevel = waterLevel + :waterAmount, lastWatered = :lastWatered, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':waterAmount': waterAmount,
            ':lastWatered': new Date().toISOString(),
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree watered successfully',
            tree: result.Attributes
        })
    };
}

async function getTreeGrowth(event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || event.queryStringParameters?.treeId || 'tree-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        }
    };

    const result = await dynamodb.get(params).promise();

    if (result.Item) {
        const tree = result.Item;
        const daysSincePlanted = Math.floor((new Date() - new Date(tree.plantedDate)) / (1000 * 60 * 60 * 24));
        const growthProgress = Math.min(100, (daysSincePlanted / 30) * 100);

        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                treeId,
                daysSincePlanted,
                growthProgress,
                currentLevel: tree.growthLevel,
                waterLevel: tree.waterLevel,
                health: tree.health
            })
        };
    }

    return {
        statusCode: 404,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({ error: 'Tree not found' })
    };
}
EOF

# 3. QuestionsFunction
mkdir -p lambda-functions/hopie-questions-dev
cat > lambda-functions/hopie-questions-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/questions')) {
            return await getQuestions(event);
        } else if (httpMethod === 'POST' && path.includes('/questions')) {
            return await createQuestion(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/question/')) {
            return await getQuestionById(event);
        } else if (httpMethod === 'PUT' && path.includes('/question/')) {
            return await updateQuestion(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/question/')) {
            return await deleteQuestion(event);
        } else if (httpMethod === 'POST' && path.includes('/question/answer')) {
            return await answerQuestion(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/daily-question')) {
            return await getDailyQuestion(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getQuestions(event) {
    const category = event.queryStringParameters?.category || 'all';

    let params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'QUESTION'
        }
    };

    if (category !== 'all') {
        params.FilterExpression = 'category = :category';
        params.ExpressionAttributeValues[':category'] = category;
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            questions: result.Items || []
        })
    };
}

async function createQuestion(body, event) {
    const questionId = `question-${Date.now()}`;
    const { text, category, type, options, correctAnswer } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA',
            GSI1PK: 'QUESTION',
            GSI1SK: questionId,
            id: questionId,
            text,
            category,
            type: type || 'multiple_choice',
            options: options || [],
            correctAnswer,
            difficulty: body.difficulty || 'medium',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Question created successfully',
            question: params.Item
        })
    };
}

async function getQuestionById(event) {
    const questionId = event.pathParameters?.id || 'question-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            question: result.Item || null
        })
    };
}

async function updateQuestion(body, event) {
    const questionId = event.pathParameters?.id || 'question-123';
    const { text, category, type, options, correctAnswer, difficulty } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #text = :text, category = :category, #type = :type, options = :options, correctAnswer = :correctAnswer, difficulty = :difficulty, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#text': 'text',
            '#type': 'type'
        },
        ExpressionAttributeValues: {
            ':text': text,
            ':category': category,
            ':type': type,
            ':options': options,
            ':correctAnswer': correctAnswer,
            ':difficulty': difficulty,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Question updated successfully',
            question: result.Attributes
        })
    };
}

async function deleteQuestion(event) {
    const questionId = event.pathParameters?.id || 'question-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Question deleted successfully'
        })
    };
}

async function answerQuestion(body, event) {
    const userId = 'user-123';
    const { questionId, answer, timeSpent } = body;
    const answerId = `answer-${Date.now()}`;

    // Get the question to check correct answer
    const questionParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `QUESTION#${questionId}`,
            SK: 'METADATA'
        }
    };

    const questionResult = await dynamodb.get(questionParams).promise();
    const isCorrect = questionResult.Item ? questionResult.Item.correctAnswer === answer : false;

    // Save the answer
    const answerParams = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `ANSWER#${answerId}`,
            id: answerId,
            userId,
            questionId,
            answer,
            isCorrect,
            timeSpent,
            answeredAt: new Date().toISOString()
        }
    };

    await dynamodb.put(answerParams).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Answer recorded successfully',
            isCorrect,
            correctAnswer: questionResult.Item?.correctAnswer,
            answer: answerParams.Item
        })
    };
}

async function getDailyQuestion(event) {
    const today = new Date().toISOString().split('T')[0];

    // Get all questions and select one based on today's date
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'QUESTION'
        }
    };

    const result = await dynamodb.query(params).promise();
    const questions = result.Items || [];

    if (questions.length === 0) {
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'No questions available' })
        };
    }

    // Use date as seed for consistent daily question
    const dateHash = today.split('-').reduce((a, b) => parseInt(a) + parseInt(b), 0);
    const questionIndex = dateHash % questions.length;
    const dailyQuestion = questions[questionIndex];

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            date: today,
            question: dailyQuestion
        })
    };
}
EOF

echo "📁 Creando las primeras 3 funciones Lambda principales..."
echo ""

# Crear las funciones Lambda
echo "🔄 Creando hopie-couple-dev..."
create_lambda "hopie-couple-dev" "HopieApp Couple Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-tree-dev..."
create_lambda "hopie-tree-dev" "HopieApp Tree Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-questions-dev..."
create_lambda "hopie-questions-dev" "HopieApp Questions Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo ""
echo "🎉 ¡Las primeras 3 funciones Lambda principales han sido creadas!"
echo ""
echo "📋 Funciones creadas:"
echo "   ✅ hopie-couple-dev"
echo "   ✅ hopie-tree-dev"
echo "   ✅ hopie-questions-dev"
echo ""
echo "🔗 Para verificar, ejecuta:"
echo "   aws lambda list-functions --query 'Functions[?starts_with(FunctionName, \`hopie-\`)].FunctionName' --output table"
echo ""
echo "📝 NOTA: Ejecuta también CREATE_REMAINING_4_LAMBDAS.sh para completar todas las funciones principales."
