AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'HopieApp - Application Layer (uses base stack resources)'

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Deployment stage
  
  BaseStackName:
    Type: String
    Default: HopieApp-dev-Base
    Description: Name of the base stack containing core resources
  
  EnableLambdaFunctions:
    Type: String
    Default: 'true'
    AllowedValues: ['true', 'false']
    Description: Enable Lambda functions deployment
  
  EnableWebSocket:
    Type: String
    Default: 'true'
    AllowedValues: ['true', 'false']
    Description: Enable WebSocket API deployment

Conditions:
  DeployLambdas: !Equals [!Ref EnableLambdaFunctions, 'true']
  DeployWebSocket: !Equals [!Ref EnableWebSocket, 'true']
  IsProd: !Equals [!Ref Stage, 'prod']

Globals:
  Function:
    Runtime: nodejs18.x
    MemorySize: 512
    Timeout: 30
    Environment:
      Variables:
        STAGE: !Ref Stage
        DYNAMODB_TABLE: 
          Fn::ImportValue: !Sub '${BaseStackName}-DynamoDBTable'
        USER_POOL_ID: 
          Fn::ImportValue: !Sub '${BaseStackName}-UserPoolId'
        USER_POOL_CLIENT_ID: 
          Fn::ImportValue: !Sub '${BaseStackName}-UserPoolClientId'
        USER_CONTENT_BUCKET: 
          Fn::ImportValue: !Sub '${BaseStackName}-UserContentBucket'
        APP_ASSETS_BUCKET: 
          Fn::ImportValue: !Sub '${BaseStackName}-AppAssetsBucket'

Resources:
  # ===== CLOUDFRONT DISTRIBUTION =====
  CloudFrontOriginAccessIdentity:
    Type: AWS::CloudFront::CloudFrontOriginAccessIdentity
    Properties:
      CloudFrontOriginAccessIdentityConfig:
        Comment: !Sub 'OAI for HopieApp ${Stage}'

  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    DependsOn: CloudFrontOriginAccessIdentity
    Properties:
      DistributionConfig:
        Comment: !Sub 'HopieApp CDN ${Stage}'
        DefaultRootObject: index.html
        Enabled: true
        HttpVersion: http2
        PriceClass: PriceClass_100
        Origins:
          - Id: UserContentOrigin
            DomainName: !Sub 
              - '${BucketName}.s3.${AWS::Region}.amazonaws.com'
              - BucketName: 
                  Fn::ImportValue: !Sub '${BaseStackName}-UserContentBucket'
            S3OriginConfig:
              OriginAccessIdentity: !Sub 'origin-access-identity/cloudfront/${CloudFrontOriginAccessIdentity}'
          - Id: AppAssetsOrigin
            DomainName: !Sub 
              - '${BucketName}.s3.${AWS::Region}.amazonaws.com'
              - BucketName: 
                  Fn::ImportValue: !Sub '${BaseStackName}-AppAssetsBucket'
            S3OriginConfig:
              OriginAccessIdentity: !Sub 'origin-access-identity/cloudfront/${CloudFrontOriginAccessIdentity}'
        DefaultCacheBehavior:
          TargetOriginId: AppAssetsOrigin
          ViewerProtocolPolicy: redirect-to-https
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
          OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf
        CacheBehaviors:
          - PathPattern: '/user-content/*'
            TargetOriginId: UserContentOrigin
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf

  # ===== BUCKET POLICIES =====
  UserContentBucketPolicy:
    Type: AWS::S3::BucketPolicy
    DependsOn: CloudFrontOriginAccessIdentity
    Properties:
      Bucket: 
        Fn::ImportValue: !Sub '${BaseStackName}-UserContentBucket'
      PolicyDocument:
        Statement:
          - Sid: AllowCloudFrontAccess
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${CloudFrontOriginAccessIdentity}'
            Action: 's3:GetObject'
            Resource: !Sub 
              - '${BucketArn}/*'
              - BucketArn: !Sub 
                - 'arn:aws:s3:::${BucketName}'
                - BucketName: 
                    Fn::ImportValue: !Sub '${BaseStackName}-UserContentBucket'

  AppAssetsBucketPolicy:
    Type: AWS::S3::BucketPolicy
    DependsOn: CloudFrontOriginAccessIdentity
    Properties:
      Bucket: 
        Fn::ImportValue: !Sub '${BaseStackName}-AppAssetsBucket'
      PolicyDocument:
        Statement:
          - Sid: AllowCloudFrontAccess
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${CloudFrontOriginAccessIdentity}'
            Action: 's3:GetObject'
            Resource: !Sub 
              - '${BucketArn}/*'
              - BucketArn: !Sub 
                - 'arn:aws:s3:::${BucketName}'
                - BucketName: 
                    Fn::ImportValue: !Sub '${BaseStackName}-AppAssetsBucket'

  # ===== API GATEWAY =====
  HopieApi:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub 'hopie-api-${Stage}'
      StageName: !Ref Stage
      Cors:
        AllowMethods: "'*'"
        AllowHeaders: "'*'"
        AllowOrigin: "'*'"
      Auth:
        DefaultAuthorizer: CognitoAuthorizer
        Authorizers:
          CognitoAuthorizer:
            UserPoolArn: !Sub 
              - 'arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${UserPoolId}'
              - UserPoolId: 
                  Fn::ImportValue: !Sub '${BaseStackName}-UserPoolId'

Outputs:
  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !Sub 'https://${HopieApi}.execute-api.${AWS::Region}.amazonaws.com/${Stage}'
    Export:
      Name: !Sub '${AWS::StackName}-ApiUrl'

  CloudFrontUrl:
    Description: CloudFront Distribution URL
    Value: !Sub 'https://${CloudFrontDistribution.DomainName}'
    Export:
      Name: !Sub '${AWS::StackName}-CloudFrontUrl'

  CloudFrontDistributionId:
    Description: CloudFront Distribution ID
    Value: !Ref CloudFrontDistribution
    Export:
      Name: !Sub '${AWS::StackName}-CloudFrontDistributionId'
