param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage,
    
    [Parameter(Mandatory=$false)]
    [switch]$BaseOnly,
    
    [Parameter(Mandatory=$false)]
    [switch]$FullStack,
    
    [Parameter(Mandatory=$false)]
    [string]$StackName = "HopieApp-$Stage"
)

# Colores para output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput $Blue "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "❌ $message"
}

function Test-StackExists($stackName) {
    try {
        aws cloudformation describe-stacks --stack-name $stackName --output text --query "Stacks[0].StackStatus" 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Deploy-Template($templateFile, $stackName, $parameters, $description) {
    Write-Info "Desplegando: $description"
    Write-Info "Template: $templateFile"
    Write-Info "Stack: $stackName"
    
    $samCommand = @(
        "sam", "deploy"
        "--template-file", $templateFile
        "--stack-name", $stackName
        "--capabilities", "CAPABILITY_IAM", "CAPABILITY_AUTO_EXPAND"
        "--disable-rollback"
        "--parameter-overrides"
    ) + $parameters
    
    Write-Info "Comando: $($samCommand -join ' ')"
    
    try {
        & $samCommand[0] $samCommand[1..($samCommand.Length-1)]
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$description desplegado exitosamente"
            return $true
        } else {
            Write-Error "$description falló con código: $LASTEXITCODE"
            
            # Mostrar eventos del stack
            Write-Info "Últimos eventos del stack:"
            aws cloudformation describe-stack-events --stack-name $stackName --max-items 10 --output table
            
            return $false
        }
    } catch {
        Write-Error "Error desplegando $description`: $_"
        return $false
    }
}

# Verificar herramientas
Write-Info "Verificando herramientas necesarias..."
try {
    $samVersion = sam --version
    Write-Success "SAM CLI: $samVersion"
    
    $awsIdentity = aws sts get-caller-identity --output json | ConvertFrom-Json
    Write-Success "AWS Account: $($awsIdentity.Account)"
} catch {
    Write-Error "Error verificando herramientas. Asegúrate de tener SAM CLI y AWS CLI configurados."
    exit 1
}

# Verificar archivos de template
if (-not (Test-Path "template-base.yaml")) {
    Write-Error "template-base.yaml no encontrado"
    exit 1
}

if (-not $BaseOnly -and -not (Test-Path "template.yaml")) {
    Write-Error "template.yaml no encontrado"
    exit 1
}

# Construir aplicación
Write-Info "Construyendo aplicación..."
sam build
if ($LASTEXITCODE -ne 0) {
    Write-Error "Error al construir la aplicación"
    exit 1
}

$baseStackName = "$StackName-Base"
$baseParams = @(
    "Stage=$Stage"
    "CognitoDomainPrefix=hopie-app-$Stage"
)

# Verificar si el stack base existe
$baseStackExists = Test-StackExists $baseStackName

if (-not $baseStackExists) {
    Write-Info "Stack base no existe. Desplegando infraestructura base..."
    
    if (-not (Deploy-Template "template-base.yaml" $baseStackName $baseParams "Infraestructura Base")) {
        Write-Error "Falló el despliegue de la infraestructura base"
        exit 1
    }
    
    Write-Success "Infraestructura base desplegada exitosamente"
} else {
    Write-Info "Stack base ya existe: $baseStackName"
}

if ($BaseOnly) {
    Write-Success "Despliegue de infraestructura base completado"
    
    # Mostrar outputs
    Write-Info "Outputs del stack base:"
    aws cloudformation describe-stacks --stack-name $baseStackName --query "Stacks[0].Outputs" --output table
    
    exit 0
}

# Desplegar stack completo
Write-Info "Desplegando stack completo..."

$fullParams = @(
    "Stage=$Stage"
    "CognitoDomainPrefix=hopie-app-$Stage"
    "EnableLambdaFunctions=true"
    "EnableWebSocket=true"
)

if ($FullStack) {
    # Despliegue completo de una vez
    if (-not (Deploy-Template "template.yaml" $StackName $fullParams "Stack Completo")) {
        Write-Error "Falló el despliegue del stack completo"
        Write-Info "La infraestructura base permanece intacta en: $baseStackName"
        exit 1
    }
} else {
    # Despliegue incremental
    Write-Info "Desplegando sin funciones Lambda primero..."
    
    $incrementalParams = @(
        "Stage=$Stage"
        "CognitoDomainPrefix=hopie-app-$Stage"
        "EnableLambdaFunctions=false"
        "EnableWebSocket=false"
    )
    
    if (-not (Deploy-Template "template.yaml" $StackName $incrementalParams "Infraestructura sin Lambdas")) {
        Write-Error "Falló el despliegue incremental"
        exit 1
    }
    
    Write-Info "Ahora desplegando con funciones Lambda..."
    
    if (-not (Deploy-Template "template.yaml" $StackName $fullParams "Stack Completo con Lambdas")) {
        Write-Error "Falló el despliegue de las funciones Lambda"
        Write-Info "La infraestructura base permanece funcional"
        exit 1
    }
}

Write-Success "🎉 ¡Despliegue completado exitosamente!"

# Mostrar outputs finales
Write-Info "Outputs del stack:"
aws cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs" --output table

Write-Info "Stacks creados:"
Write-Success "Base: $baseStackName"
Write-Success "Completo: $StackName"

Write-Info "Para eliminar todo:"
Write-Warning "aws cloudformation delete-stack --stack-name $StackName"
Write-Warning "aws cloudformation delete-stack --stack-name $baseStackName"
