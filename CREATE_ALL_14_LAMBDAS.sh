#!/bin/bash

# 🚀 Script para crear las 13 funciones Lambda restantes de HopieApp
# (Excluyendo hopie-auth-dev que ya existe)

TABLE_NAME="hopie-main-table-dev"
USER_POOL_ID="us-east-2_gTZjXep7j"
CLIENT_ID="63604alcg1rvili3ksfan61fid"
REGION="us-east-2"
BUCKET_NAME="hopie-app-assets-dev-123456"
ROLE_ARN="arn:aws:iam::864899858226:role/hopie-lambda-execution-role"

echo "🚀 Creando las 13 funciones Lambda restantes para HopieApp..."
echo "📋 Variables configuradas:"
echo "   TABLE_NAME: $TABLE_NAME"
echo "   USER_POOL_ID: $USER_POOL_ID"
echo "   CLIENT_ID: $CLIENT_ID"
echo "   REGION: $REGION"
echo "   BUCKET_NAME: $BUCKET_NAME"
echo "   ROLE_ARN: $ROLE_ARN"
echo ""
echo "⚠️  NOTA: hopie-auth-dev ya existe, se omite de la creación"
echo ""

# Función para crear Lambda
create_lambda() {
    local FUNCTION_NAME=$1
    local DESCRIPTION=$2
    local ENV_VARS=$3
    
    echo "📦 Creando función: $FUNCTION_NAME"
    
    # Crear archivo ZIP temporal
    cd lambda-functions/$FUNCTION_NAME
    zip -r ../../${FUNCTION_NAME}.zip . > /dev/null 2>&1
    cd ../..
    
    # Crear función Lambda
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://${FUNCTION_NAME}.zip \
        --description "$DESCRIPTION" \
        --timeout 30 \
        --memory-size 256 \
        --environment Variables="$ENV_VARS" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $FUNCTION_NAME creada exitosamente"
    else
        echo "   ❌ Error creando $FUNCTION_NAME"
    fi
    
    # Limpiar archivo ZIP
    rm ${FUNCTION_NAME}.zip
}

# Crear directorio para funciones
mkdir -p lambda-functions

# 1. UserFunction
mkdir -p lambda-functions/hopie-user-dev
cat > lambda-functions/hopie-user-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/profile')) {
            return await getUserProfile(event);
        } else if (httpMethod === 'PUT' && path.includes('/profile')) {
            return await updateUserProfile(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/preferences')) {
            return await getUserPreferences(event);
        } else if (httpMethod === 'PUT' && path.includes('/preferences')) {
            return await updateUserPreferences(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUserProfile(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            user: result.Item || { id: userId, name: 'Usuario Demo', email: '<EMAIL>' }
        })
    };
}

async function updateUserProfile(body, event) {
    const userId = 'user-123';
    const { name, email, phone, birthDate } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        },
        UpdateExpression: 'SET #name = :name, email = :email, phone = :phone, birthDate = :birthDate, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':email': email,
            ':phone': phone,
            ':birthDate': birthDate,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Profile updated successfully',
            user: result.Attributes
        })
    };
}

async function getUserPreferences(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            preferences: result.Item || {
                notifications: true,
                theme: 'light',
                language: 'es'
            }
        })
    };
}

async function updateUserPreferences(body, event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        },
        UpdateExpression: 'SET preferences = :preferences, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':preferences': body,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Preferences updated successfully',
            preferences: result.Attributes
        })
    };
}
EOF

echo "📁 Creando funciones básicas (1-2)..."
create_lambda "hopie-auth-dev" "HopieApp Authentication Function" "{TABLE_NAME=$TABLE_NAME,USER_POOL_ID=$USER_POOL_ID,CLIENT_ID=$CLIENT_ID,REGION=$REGION}"
create_lambda "hopie-user-dev" "HopieApp User Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo ""
echo "🎉 ¡Primeras 2 funciones Lambda creadas!"
echo "📋 Funciones creadas hasta ahora:"
echo "   ✅ hopie-auth-dev"
echo "   ✅ hopie-user-dev"
echo ""
echo "🔗 Para verificar, ejecuta:"
echo "   aws lambda list-functions --query 'Functions[?starts_with(FunctionName, \`hopie-\`)].FunctionName' --output table"
echo ""
echo "📝 NOTA: Este script crea las primeras 2 funciones."
echo "   Para crear las 12 restantes, ejecuta los scripts adicionales."
