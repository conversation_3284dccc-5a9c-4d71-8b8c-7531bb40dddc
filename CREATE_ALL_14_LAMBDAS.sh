#!/bin/bash

# 🚀 Script para crear las 13 funciones Lambda restantes de HopieApp
# (Excluyendo hopie-auth-dev que ya existe)

TABLE_NAME="hopie-main-table-dev"
USER_POOL_ID="us-east-2_gTZjXep7j"
CLIENT_ID="63604alcg1rvili3ksfan61fid"
REGION="us-east-2"
BUCKET_NAME="hopie-app-assets-dev-123456"
ROLE_ARN="arn:aws:iam::864899858226:role/hopie-lambda-execution-role"

echo "🚀 Creando las 13 funciones Lambda restantes para HopieApp..."
echo "📋 Variables configuradas:"
echo "   TABLE_NAME: $TABLE_NAME"
echo "   USER_POOL_ID: $USER_POOL_ID"
echo "   CLIENT_ID: $CLIENT_ID"
echo "   REGION: $REGION"
echo "   BUCKET_NAME: $BUCKET_NAME"
echo "   ROLE_ARN: $ROLE_ARN"
echo ""
echo "⚠️  NOTA: hopie-auth-dev ya existe, se omite de la creación"
echo ""

# Función para crear Lambda
create_lambda() {
    local FUNCTION_NAME=$1
    local DESCRIPTION=$2
    local ENV_VARS=$3
    
    echo "📦 Creando función: $FUNCTION_NAME"
    
    # Crear archivo ZIP temporal
    cd lambda-functions/$FUNCTION_NAME
    zip -r ../../${FUNCTION_NAME}.zip . > /dev/null 2>&1
    cd ../..
    
    # Crear función Lambda
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://${FUNCTION_NAME}.zip \
        --description "$DESCRIPTION" \
        --timeout 30 \
        --memory-size 256 \
        --environment Variables="$ENV_VARS" \
        --region $REGION > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $FUNCTION_NAME creada exitosamente"
    else
        echo "   ❌ Error creando $FUNCTION_NAME"
    fi
    
    # Limpiar archivo ZIP
    rm ${FUNCTION_NAME}.zip
}

# Crear directorio para funciones
mkdir -p lambda-functions

# 1. UserFunction
mkdir -p lambda-functions/hopie-user-dev
cat > lambda-functions/hopie-user-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/profile')) {
            return await getUserProfile(event);
        } else if (httpMethod === 'PUT' && path.includes('/profile')) {
            return await updateUserProfile(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/preferences')) {
            return await getUserPreferences(event);
        } else if (httpMethod === 'PUT' && path.includes('/preferences')) {
            return await updateUserPreferences(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUserProfile(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            user: result.Item || { id: userId, name: 'Usuario Demo', email: '<EMAIL>' }
        })
    };
}

async function updateUserProfile(body, event) {
    const userId = 'user-123';
    const { name, email, phone, birthDate } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        },
        UpdateExpression: 'SET #name = :name, email = :email, phone = :phone, birthDate = :birthDate, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':email': email,
            ':phone': phone,
            ':birthDate': birthDate,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Profile updated successfully',
            user: result.Attributes
        })
    };
}

async function getUserPreferences(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            preferences: result.Item || {
                notifications: true,
                theme: 'light',
                language: 'es'
            }
        })
    };
}

async function updateUserPreferences(body, event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        },
        UpdateExpression: 'SET preferences = :preferences, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':preferences': body,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Preferences updated successfully',
            preferences: result.Attributes
        })
    };
}
EOF

# 2. RecipeFunction
mkdir -p lambda-functions/hopie-recipe-dev
cat > lambda-functions/hopie-recipe-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/recipes')) {
            return await getRecipes(event);
        } else if (httpMethod === 'POST' && path.includes('/recipes')) {
            return await createRecipe(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/recipe/')) {
            return await getRecipeById(event);
        } else if (httpMethod === 'PUT' && path.includes('/recipe/')) {
            return await updateRecipe(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/recipe/')) {
            return await deleteRecipe(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getRecipes(event) {
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'RECIPE'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            recipes: result.Items || []
        })
    };
}

async function createRecipe(body, event) {
    const recipeId = `recipe-${Date.now()}`;
    const { title, description, ingredients, instructions, cookingTime, difficulty } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA',
            GSI1PK: 'RECIPE',
            GSI1SK: recipeId,
            id: recipeId,
            title,
            description,
            ingredients,
            instructions,
            cookingTime,
            difficulty,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Recipe created successfully',
            recipe: params.Item
        })
    };
}

async function getRecipeById(event) {
    const recipeId = event.pathParameters?.id || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            recipe: result.Item || null
        })
    };
}

async function updateRecipe(body, event) {
    const recipeId = event.pathParameters?.id || 'recipe-123';
    const { title, description, ingredients, instructions, cookingTime, difficulty } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET title = :title, description = :description, ingredients = :ingredients, instructions = :instructions, cookingTime = :cookingTime, difficulty = :difficulty, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':title': title,
            ':description': description,
            ':ingredients': ingredients,
            ':instructions': instructions,
            ':cookingTime': cookingTime,
            ':difficulty': difficulty,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Recipe updated successfully',
            recipe: result.Attributes
        })
    };
}

async function deleteRecipe(event) {
    const recipeId = event.pathParameters?.id || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Recipe deleted successfully'
        })
    };
}
EOF

# 3. IngredientFunction
mkdir -p lambda-functions/hopie-ingredient-dev
cat > lambda-functions/hopie-ingredient-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/ingredients')) {
            return await getIngredients(event);
        } else if (httpMethod === 'POST' && path.includes('/ingredients')) {
            return await createIngredient(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/ingredient/')) {
            return await getIngredientById(event);
        } else if (httpMethod === 'PUT' && path.includes('/ingredient/')) {
            return await updateIngredient(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/ingredient/')) {
            return await deleteIngredient(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getIngredients(event) {
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'INGREDIENT'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            ingredients: result.Items || []
        })
    };
}

async function createIngredient(body, event) {
    const ingredientId = `ingredient-${Date.now()}`;
    const { name, category, unit, nutritionalInfo } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA',
            GSI1PK: 'INGREDIENT',
            GSI1SK: ingredientId,
            id: ingredientId,
            name,
            category,
            unit,
            nutritionalInfo,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Ingredient created successfully',
            ingredient: params.Item
        })
    };
}

async function getIngredientById(event) {
    const ingredientId = event.pathParameters?.id || 'ingredient-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            ingredient: result.Item || null
        })
    };
}

async function updateIngredient(body, event) {
    const ingredientId = event.pathParameters?.id || 'ingredient-123';
    const { name, category, unit, nutritionalInfo } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #name = :name, category = :category, unit = :unit, nutritionalInfo = :nutritionalInfo, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':category': category,
            ':unit': unit,
            ':nutritionalInfo': nutritionalInfo,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Ingredient updated successfully',
            ingredient: result.Attributes
        })
    };
}

async function deleteIngredient(event) {
    const ingredientId = event.pathParameters?.id || 'ingredient-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `INGREDIENT#${ingredientId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Ingredient deleted successfully'
        })
    };
}
EOF

# 4. ShoppingListFunction
mkdir -p lambda-functions/hopie-shopping-list-dev
cat > lambda-functions/hopie-shopping-list-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/shopping-lists')) {
            return await getShoppingLists(event);
        } else if (httpMethod === 'POST' && path.includes('/shopping-lists')) {
            return await createShoppingList(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/shopping-list/')) {
            return await getShoppingListById(event);
        } else if (httpMethod === 'PUT' && path.includes('/shopping-list/')) {
            return await updateShoppingList(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/shopping-list/')) {
            return await deleteShoppingList(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getShoppingLists(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'SHOPPING_LIST#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            shoppingLists: result.Items || []
        })
    };
}

async function createShoppingList(body, event) {
    const userId = 'user-123';
    const listId = `list-${Date.now()}`;
    const { name, items } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `SHOPPING_LIST#${listId}`,
            id: listId,
            name,
            items: items || [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Shopping list created successfully',
            shoppingList: params.Item
        })
    };
}

async function getShoppingListById(event) {
    const userId = 'user-123';
    const listId = event.pathParameters?.id || 'list-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SHOPPING_LIST#${listId}`
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            shoppingList: result.Item || null
        })
    };
}

async function updateShoppingList(body, event) {
    const userId = 'user-123';
    const listId = event.pathParameters?.id || 'list-123';
    const { name, items } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SHOPPING_LIST#${listId}`
        },
        UpdateExpression: 'SET #name = :name, items = :items, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':items': items,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Shopping list updated successfully',
            shoppingList: result.Attributes
        })
    };
}

async function deleteShoppingList(event) {
    const userId = 'user-123';
    const listId = event.pathParameters?.id || 'list-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SHOPPING_LIST#${listId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Shopping list deleted successfully'
        })
    };
}
EOF

# 5. MealPlanFunction
mkdir -p lambda-functions/hopie-meal-plan-dev
cat > lambda-functions/hopie-meal-plan-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/meal-plans')) {
            return await getMealPlans(event);
        } else if (httpMethod === 'POST' && path.includes('/meal-plans')) {
            return await createMealPlan(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/meal-plan/')) {
            return await getMealPlanById(event);
        } else if (httpMethod === 'PUT' && path.includes('/meal-plan/')) {
            return await updateMealPlan(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/meal-plan/')) {
            return await deleteMealPlan(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getMealPlans(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'MEAL_PLAN#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            mealPlans: result.Items || []
        })
    };
}

async function createMealPlan(body, event) {
    const userId = 'user-123';
    const planId = `plan-${Date.now()}`;
    const { name, startDate, endDate, meals } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`,
            id: planId,
            name,
            startDate,
            endDate,
            meals: meals || {},
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Meal plan created successfully',
            mealPlan: params.Item
        })
    };
}

async function getMealPlanById(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'plan-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            mealPlan: result.Item || null
        })
    };
}

async function updateMealPlan(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'plan-123';
    const { name, startDate, endDate, meals } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`
        },
        UpdateExpression: 'SET #name = :name, startDate = :startDate, endDate = :endDate, meals = :meals, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':startDate': startDate,
            ':endDate': endDate,
            ':meals': meals,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Meal plan updated successfully',
            mealPlan: result.Attributes
        })
    };
}

async function deleteMealPlan(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'plan-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Meal plan deleted successfully'
        })
    };
}
EOF

# 6. NotificationFunction
mkdir -p lambda-functions/hopie-notification-dev
cat > lambda-functions/hopie-notification-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/notifications')) {
            return await getNotifications(event);
        } else if (httpMethod === 'POST' && path.includes('/notifications')) {
            return await createNotification(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/notification/')) {
            return await markAsRead(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/notification/')) {
            return await deleteNotification(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getNotifications(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'NOTIFICATION#'
        },
        ScanIndexForward: false
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            notifications: result.Items || []
        })
    };
}

async function createNotification(body, event) {
    const userId = 'user-123';
    const notificationId = `notification-${Date.now()}`;
    const { title, message, type } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `NOTIFICATION#${notificationId}`,
            id: notificationId,
            title,
            message,
            type: type || 'info',
            read: false,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Notification created successfully',
            notification: params.Item
        })
    };
}

async function markAsRead(body, event) {
    const userId = 'user-123';
    const notificationId = event.pathParameters?.id || 'notification-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `NOTIFICATION#${notificationId}`
        },
        UpdateExpression: 'SET #read = :read, readAt = :readAt',
        ExpressionAttributeNames: {
            '#read': 'read'
        },
        ExpressionAttributeValues: {
            ':read': true,
            ':readAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Notification marked as read',
            notification: result.Attributes
        })
    };
}

async function deleteNotification(event) {
    const userId = 'user-123';
    const notificationId = event.pathParameters?.id || 'notification-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `NOTIFICATION#${notificationId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Notification deleted successfully'
        })
    };
}
EOF

# 7. SearchFunction
mkdir -p lambda-functions/hopie-search-dev
cat > lambda-functions/hopie-search-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, queryStringParameters } = event;

    try {
        if (httpMethod === 'GET' && path.includes('/search')) {
            return await searchItems(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function searchItems(event) {
    const query = event.queryStringParameters?.q || '';
    const type = event.queryStringParameters?.type || 'all';

    if (!query) {
        return {
            statusCode: 400,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Query parameter is required' })
        };
    }

    let results = [];

    // Search recipes
    if (type === 'all' || type === 'recipes') {
        const recipeParams = {
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk',
            FilterExpression: 'contains(title, :query) OR contains(description, :query)',
            ExpressionAttributeValues: {
                ':gsi1pk': 'RECIPE',
                ':query': query
            }
        };

        const recipeResult = await dynamodb.query(recipeParams).promise();
        results = results.concat(recipeResult.Items.map(item => ({ ...item, type: 'recipe' })));
    }

    // Search ingredients
    if (type === 'all' || type === 'ingredients') {
        const ingredientParams = {
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk',
            FilterExpression: 'contains(#name, :query)',
            ExpressionAttributeNames: {
                '#name': 'name'
            },
            ExpressionAttributeValues: {
                ':gsi1pk': 'INGREDIENT',
                ':query': query
            }
        };

        const ingredientResult = await dynamodb.query(ingredientParams).promise();
        results = results.concat(ingredientResult.Items.map(item => ({ ...item, type: 'ingredient' })));
    }

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            query,
            type,
            results: results || [],
            count: results.length
        })
    };
}
EOF

# 8. FavoriteFunction
mkdir -p lambda-functions/hopie-favorite-dev
cat > lambda-functions/hopie-favorite-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/favorites')) {
            return await getFavorites(event);
        } else if (httpMethod === 'POST' && path.includes('/favorites')) {
            return await addFavorite(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/favorite/')) {
            return await removeFavorite(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getFavorites(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'FAVORITE#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            favorites: result.Items || []
        })
    };
}

async function addFavorite(body, event) {
    const userId = 'user-123';
    const { itemId, itemType, title } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `FAVORITE#${itemType}#${itemId}`,
            itemId,
            itemType,
            title,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Favorite added successfully',
            favorite: params.Item
        })
    };
}

async function removeFavorite(event) {
    const userId = 'user-123';
    const favoriteId = event.pathParameters?.id || 'favorite-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `FAVORITE#${favoriteId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Favorite removed successfully'
        })
    };
}
EOF

# 9. ReviewFunction
mkdir -p lambda-functions/hopie-review-dev
cat > lambda-functions/hopie-review-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/reviews')) {
            return await getReviews(event);
        } else if (httpMethod === 'POST' && path.includes('/reviews')) {
            return await createReview(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/review/')) {
            return await updateReview(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/review/')) {
            return await deleteReview(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getReviews(event) {
    const recipeId = event.queryStringParameters?.recipeId || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `RECIPE#${recipeId}`,
            ':sk': 'REVIEW#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            reviews: result.Items || []
        })
    };
}

async function createReview(body, event) {
    const userId = 'user-123';
    const reviewId = `review-${Date.now()}`;
    const { recipeId, rating, comment } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `RECIPE#${recipeId}`,
            SK: `REVIEW#${reviewId}`,
            id: reviewId,
            userId,
            recipeId,
            rating,
            comment,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Review created successfully',
            review: params.Item
        })
    };
}

async function updateReview(body, event) {
    const reviewId = event.pathParameters?.id || 'review-123';
    const { recipeId, rating, comment } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: `REVIEW#${reviewId}`
        },
        UpdateExpression: 'SET rating = :rating, comment = :comment, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':rating': rating,
            ':comment': comment,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Review updated successfully',
            review: result.Attributes
        })
    };
}

async function deleteReview(event) {
    const reviewId = event.pathParameters?.id || 'review-123';
    const recipeId = event.queryStringParameters?.recipeId || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: `REVIEW#${reviewId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Review deleted successfully'
        })
    };
}
EOF

# 10. CategoryFunction
mkdir -p lambda-functions/hopie-category-dev
cat > lambda-functions/hopie-category-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/categories')) {
            return await getCategories(event);
        } else if (httpMethod === 'POST' && path.includes('/categories')) {
            return await createCategory(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/category/')) {
            return await getCategoryById(event);
        } else if (httpMethod === 'PUT' && path.includes('/category/')) {
            return await updateCategory(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/category/')) {
            return await deleteCategory(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getCategories(event) {
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'CATEGORY'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            categories: result.Items || []
        })
    };
}

async function createCategory(body, event) {
    const categoryId = `category-${Date.now()}`;
    const { name, description, color } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA',
            GSI1PK: 'CATEGORY',
            GSI1SK: categoryId,
            id: categoryId,
            name,
            description,
            color,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Category created successfully',
            category: params.Item
        })
    };
}

async function getCategoryById(event) {
    const categoryId = event.pathParameters?.id || 'category-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            category: result.Item || null
        })
    };
}

async function updateCategory(body, event) {
    const categoryId = event.pathParameters?.id || 'category-123';
    const { name, description, color } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #name = :name, description = :description, color = :color, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':description': description,
            ':color': color,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Category updated successfully',
            category: result.Attributes
        })
    };
}

async function deleteCategory(event) {
    const categoryId = event.pathParameters?.id || 'category-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Category deleted successfully'
        })
    };
}
EOF

# 11. ImageFunction
mkdir -p lambda-functions/hopie-image-dev
cat > lambda-functions/hopie-image-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const s3 = new AWS.S3();
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;
const BUCKET_NAME = process.env.BUCKET_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/upload-url')) {
            return await getUploadUrl(requestBody, event);
        } else if (httpMethod === 'POST' && path.includes('/images')) {
            return await saveImageMetadata(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/images')) {
            return await getImages(event);
        } else if (httpMethod === 'DELETE' && path.includes('/image/')) {
            return await deleteImage(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUploadUrl(body, event) {
    const { fileName, fileType } = body;
    const key = `images/${Date.now()}-${fileName}`;

    const params = {
        Bucket: BUCKET_NAME,
        Key: key,
        ContentType: fileType,
        Expires: 300
    };

    const uploadUrl = s3.getSignedUrl('putObject', params);

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            uploadUrl,
            key,
            downloadUrl: `https://${BUCKET_NAME}.s3.amazonaws.com/${key}`
        })
    };
}

async function saveImageMetadata(body, event) {
    const imageId = `image-${Date.now()}`;
    const { key, fileName, fileType, entityId, entityType } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `IMAGE#${imageId}`,
            SK: 'METADATA',
            GSI1PK: `${entityType}#${entityId}`,
            GSI1SK: `IMAGE#${imageId}`,
            id: imageId,
            key,
            fileName,
            fileType,
            entityId,
            entityType,
            url: `https://${BUCKET_NAME}.s3.amazonaws.com/${key}`,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Image metadata saved successfully',
            image: params.Item
        })
    };
}

async function getImages(event) {
    const entityId = event.queryStringParameters?.entityId;
    const entityType = event.queryStringParameters?.entityType;

    if (!entityId || !entityType) {
        return {
            statusCode: 400,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'entityId and entityType are required' })
        };
    }

    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk AND begins_with(GSI1SK, :gsi1sk)',
        ExpressionAttributeValues: {
            ':gsi1pk': `${entityType}#${entityId}`,
            ':gsi1sk': 'IMAGE#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            images: result.Items || []
        })
    };
}

async function deleteImage(event) {
    const imageId = event.pathParameters?.id || 'image-123';

    // Get image metadata first
    const getParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `IMAGE#${imageId}`,
            SK: 'METADATA'
        }
    };

    const imageResult = await dynamodb.get(getParams).promise();

    if (imageResult.Item) {
        // Delete from S3
        const deleteS3Params = {
            Bucket: BUCKET_NAME,
            Key: imageResult.Item.key
        };

        await s3.deleteObject(deleteS3Params).promise();

        // Delete from DynamoDB
        await dynamodb.delete(getParams).promise();
    }

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Image deleted successfully'
        })
    };
}
EOF

# 12. AnalyticsFunction
mkdir -p lambda-functions/hopie-analytics-dev
cat > lambda-functions/hopie-analytics-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/analytics/event')) {
            return await trackEvent(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/analytics/dashboard')) {
            return await getDashboardData(event);
        } else if (httpMethod === 'GET' && path.includes('/analytics/user-stats')) {
            return await getUserStats(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function trackEvent(body, event) {
    const userId = 'user-123';
    const eventId = `event-${Date.now()}`;
    const { eventType, eventData, timestamp } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ANALYTICS#${userId}`,
            SK: `EVENT#${eventId}`,
            id: eventId,
            userId,
            eventType,
            eventData,
            timestamp: timestamp || new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Event tracked successfully',
            event: params.Item
        })
    };
}

async function getDashboardData(event) {
    const userId = 'user-123';

    // Get recent events
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ANALYTICS#${userId}`,
            ':sk': 'EVENT#'
        },
        ScanIndexForward: false,
        Limit: 50
    };

    const result = await dynamodb.query(params).promise();

    // Process events for dashboard
    const events = result.Items || [];
    const eventTypes = {};

    events.forEach(event => {
        eventTypes[event.eventType] = (eventTypes[event.eventType] || 0) + 1;
    });

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            totalEvents: events.length,
            eventTypes,
            recentEvents: events.slice(0, 10)
        })
    };
}

async function getUserStats(event) {
    const userId = 'user-123';

    // Get user's recipes count
    const recipeParams = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'RECIPE'
        }
    };

    const recipeResult = await dynamodb.query(recipeParams).promise();

    // Get user's favorites count
    const favoriteParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'FAVORITE#'
        }
    };

    const favoriteResult = await dynamodb.query(favoriteParams).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            recipesCount: recipeResult.Items?.length || 0,
            favoritesCount: favoriteResult.Items?.length || 0,
            joinDate: '2024-01-01',
            lastActivity: new Date().toISOString()
        })
    };
}
EOF

# 13. ReportFunction
mkdir -p lambda-functions/hopie-report-dev
cat > lambda-functions/hopie-report-dev/index.js << 'EOF'
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/reports')) {
            return await createReport(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/reports')) {
            return await getReports(event);
        } else if (httpMethod === 'GET' && path.includes('/report/')) {
            return await getReportById(event);
        } else if (httpMethod === 'PUT' && path.includes('/report/')) {
            return await updateReportStatus(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function createReport(body, event) {
    const reportId = `report-${Date.now()}`;
    const userId = 'user-123';
    const { entityId, entityType, reason, description } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `REPORT#${reportId}`,
            SK: 'METADATA',
            GSI1PK: 'REPORT',
            GSI1SK: reportId,
            id: reportId,
            userId,
            entityId,
            entityType,
            reason,
            description,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Report created successfully',
            report: params.Item
        })
    };
}

async function getReports(event) {
    const status = event.queryStringParameters?.status || 'all';

    let params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'REPORT'
        }
    };

    if (status !== 'all') {
        params.FilterExpression = '#status = :status';
        params.ExpressionAttributeNames = { '#status': 'status' };
        params.ExpressionAttributeValues[':status'] = status;
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            reports: result.Items || []
        })
    };
}

async function getReportById(event) {
    const reportId = event.pathParameters?.id || 'report-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `REPORT#${reportId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            report: result.Item || null
        })
    };
}

async function updateReportStatus(body, event) {
    const reportId = event.pathParameters?.id || 'report-123';
    const { status, adminNotes } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `REPORT#${reportId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #status = :status, adminNotes = :adminNotes, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#status': 'status'
        },
        ExpressionAttributeValues: {
            ':status': status,
            ':adminNotes': adminNotes,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Report status updated successfully',
            report: result.Attributes
        })
    };
}
EOF

echo "📁 Creando las 13 funciones Lambda restantes..."
echo ""

# Crear todas las funciones Lambda
echo "🔄 Creando hopie-user-dev..."
create_lambda "hopie-user-dev" "HopieApp User Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-recipe-dev..."
create_lambda "hopie-recipe-dev" "HopieApp Recipe Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-ingredient-dev..."
create_lambda "hopie-ingredient-dev" "HopieApp Ingredient Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-shopping-list-dev..."
create_lambda "hopie-shopping-list-dev" "HopieApp Shopping List Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-meal-plan-dev..."
create_lambda "hopie-meal-plan-dev" "HopieApp Meal Plan Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-notification-dev..."
create_lambda "hopie-notification-dev" "HopieApp Notification Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-search-dev..."
create_lambda "hopie-search-dev" "HopieApp Search Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-favorite-dev..."
create_lambda "hopie-favorite-dev" "HopieApp Favorite Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-review-dev..."
create_lambda "hopie-review-dev" "HopieApp Review Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-category-dev..."
create_lambda "hopie-category-dev" "HopieApp Category Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-image-dev..."
create_lambda "hopie-image-dev" "HopieApp Image Management Function" "{TABLE_NAME=$TABLE_NAME,BUCKET_NAME=$BUCKET_NAME}"

echo "🔄 Creando hopie-analytics-dev..."
create_lambda "hopie-analytics-dev" "HopieApp Analytics Function" "{TABLE_NAME=$TABLE_NAME}"

echo "🔄 Creando hopie-report-dev..."
create_lambda "hopie-report-dev" "HopieApp Report Management Function" "{TABLE_NAME=$TABLE_NAME}"

echo ""
echo "🎉 ¡Todas las 13 funciones Lambda han sido creadas exitosamente!"
echo ""
echo "📋 Funciones creadas:"
echo "   ✅ hopie-user-dev"
echo "   ✅ hopie-recipe-dev"
echo "   ✅ hopie-ingredient-dev"
echo "   ✅ hopie-shopping-list-dev"
echo "   ✅ hopie-meal-plan-dev"
echo "   ✅ hopie-notification-dev"
echo "   ✅ hopie-search-dev"
echo "   ✅ hopie-favorite-dev"
echo "   ✅ hopie-review-dev"
echo "   ✅ hopie-category-dev"
echo "   ✅ hopie-image-dev"
echo "   ✅ hopie-analytics-dev"
echo "   ✅ hopie-report-dev"
echo ""
echo "⚠️  NOTA: hopie-auth-dev ya existía y se omitió de la creación."
echo ""
echo "🔗 Para verificar todas las funciones, ejecuta:"
echo "   aws lambda list-functions --query 'Functions[?starts_with(FunctionName, \`hopie-\`)].FunctionName' --output table"
echo ""
echo "🚀 ¡HopieApp está listo para usar!"
