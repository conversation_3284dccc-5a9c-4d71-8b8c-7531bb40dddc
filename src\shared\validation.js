const Joi = require('joi');

/**
 * Validation schemas for HopieApp
 */

// Common schemas
const commonSchemas = {
  id: Joi.string().uuid().required(),
  email: Joi.string().email().required(),
  name: Joi.string().min(2).max(50).required(),
  password: Joi.string().min(8).max(128).required(),
  coordinates: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required()
  }),
  pagination: Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(20),
    page: Joi.number().integer().min(1).default(1),
    nextToken: Joi.string().optional()
  })
};

// Auth schemas
const authSchemas = {
  register: Joi.object({
    email: commonSchemas.email,
    password: commonSchemas.password,
    name: commonSchemas.name,
    confirmPassword: Joi.string().valid(Joi.ref('password')).required()
  }),

  login: Joi.object({
    email: commonSchemas.email,
    password: Joi.string().required()
  }),

  updateProfile: Joi.object({
    name: commonSchemas.name.optional(),
    avatarUrl: Joi.string().uri().optional(),
    settings: Joi.object({
      notifications: Joi.boolean().optional(),
      locationSharing: Joi.boolean().optional()
    }).optional()
  })
};

// Couple schemas
const coupleSchemas = {
  invite: Joi.object({
    email: commonSchemas.email
  }),

  updateSettings: Joi.object({
    treeType: Joi.string().valid('roble', 'cerezo', 'pino', 'sauce', 'olivo').optional(),
    notifications: Joi.boolean().optional(),
    privacy: Joi.object({
      shareLocation: Joi.boolean().optional(),
      shareActivity: Joi.boolean().optional()
    }).optional()
  })
};

// Tree schemas
const treeSchemas = {
  selectType: Joi.object({
    treeType: Joi.string().valid('roble', 'cerezo', 'pino', 'sauce', 'olivo').required()
  }),

  water: Joi.object({
    message: Joi.string().max(200).optional()
  })
};

// Questions schemas
const questionSchemas = {
  answer: Joi.object({
    questionId: Joi.string().required(),
    answer: Joi.string().min(1).max(1000).required()
  })
};

// Life plan schemas
const lifePlanSchemas = {
  createStage: Joi.object({
    title: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(500).optional(),
    targetDate: Joi.date().iso().optional(),
    order: Joi.number().integer().min(1).optional(),
    coverImageUrl: Joi.string().uri().optional()
  }),

  updateStage: Joi.object({
    title: Joi.string().min(1).max(100).optional(),
    description: Joi.string().max(500).optional(),
    targetDate: Joi.date().iso().optional(),
    status: Joi.string().valid('pending', 'in_progress', 'completed').optional(),
    notes: Joi.string().max(1000).optional(),
    order: Joi.number().integer().min(1).optional(),
    coverImageUrl: Joi.string().uri().optional()
  })
};

// Places schemas
const placeSchemas = {
  create: Joi.object({
    name: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(500).optional(),
    category: Joi.string().valid('home', 'restaurant', 'cafe', 'park', 'beach', 'mountain', 'city', 'other').required(),
    coordinates: commonSchemas.coordinates.required(),
    address: Joi.string().max(200).optional(),
    mainImageUrl: Joi.string().uri().optional(),
    galleryUrls: Joi.array().items(Joi.string().uri()).max(10).optional()
  }),

  update: Joi.object({
    name: Joi.string().min(1).max(100).optional(),
    description: Joi.string().max(500).optional(),
    category: Joi.string().valid('home', 'restaurant', 'cafe', 'park', 'beach', 'mountain', 'city', 'other').optional(),
    coordinates: commonSchemas.coordinates.optional(),
    address: Joi.string().max(200).optional(),
    mainImageUrl: Joi.string().uri().optional(),
    galleryUrls: Joi.array().items(Joi.string().uri()).max(10).optional(),
    isFavorite: Joi.boolean().optional()
  }),

  visit: Joi.object({
    notes: Joi.string().max(500).optional(),
    rating: Joi.number().min(1).max(5).optional(),
    visitDate: Joi.date().iso().optional()
  })
};

// Chat schemas
const chatSchemas = {
  sendMessage: Joi.object({
    content: Joi.string().min(1).max(1000).required(),
    type: Joi.string().valid('text', 'image', 'location').default('text'),
    attachmentUrl: Joi.string().uri().optional()
  }),

  markAsRead: Joi.object({
    messageIds: Joi.array().items(Joi.string()).min(1).required()
  })
};

// Location schemas
const locationSchemas = {
  share: Joi.object({
    coordinates: commonSchemas.coordinates.required(),
    accuracy: Joi.number().min(0).optional(),
    address: Joi.string().max(200).optional(),
    duration: Joi.number().integer().min(1).max(1440).default(60) // minutes
  }),

  updateSettings: Joi.object({
    isSharing: Joi.boolean().required(),
    shareWithPartner: Joi.boolean().optional(),
    accuracy: Joi.string().valid('high', 'medium', 'low').optional()
  })
};

// Image schemas
const imageSchemas = {
  uploadUrl: Joi.object({
    fileName: Joi.string().required(),
    fileType: Joi.string().valid('image/jpeg', 'image/png', 'image/webp').required(),
    category: Joi.string().valid('avatar', 'place', 'lifeplan', 'chat').required(),
    entityId: Joi.string().optional()
  })
};

// Notification schemas
const notificationSchemas = {
  registerDevice: Joi.object({
    deviceToken: Joi.string().required(),
    platform: Joi.string().valid('ios', 'android', 'web').required()
  }),

  updateSettings: Joi.object({
    pushNotifications: Joi.boolean().optional(),
    emailNotifications: Joi.boolean().optional(),
    types: Joi.object({
      treeWatering: Joi.boolean().optional(),
      dailyQuestions: Joi.boolean().optional(),
      chatMessages: Joi.boolean().optional(),
      partnerActivity: Joi.boolean().optional()
    }).optional()
  })
};

/**
 * Validate request body against schema
 */
function validateBody(body, schema) {
  const { error, value } = schema.validate(body, {
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    return { isValid: false, errors };
  }

  return { isValid: true, value };
}

/**
 * Validate query parameters
 */
function validateQuery(query, schema) {
  return validateBody(query, schema);
}

/**
 * Validate path parameters
 */
function validateParams(params, schema) {
  return validateBody(params, schema);
}

/**
 * Middleware to validate request
 */
function validate(schema, source = 'body') {
  return (req, res, next) => {
    const data = source === 'body' ? req.body :
      source === 'query' ? req.query :
        req.params;

    const validation = validateBody(data, schema);

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validation.errors
        }
      });
    }

    req.validated = validation.value;
    next();
  };
}

module.exports = {
  schemas: {
    common: commonSchemas,
    auth: authSchemas,
    couple: coupleSchemas,
    tree: treeSchemas,
    question: questionSchemas,
    lifePlan: lifePlanSchemas,
    place: placeSchemas,
    chat: chatSchemas,
    location: locationSchemas,
    image: imageSchemas,
    notification: notificationSchemas
  },
  validateBody,
  validateQuery,
  validateParams,
  validate
};
