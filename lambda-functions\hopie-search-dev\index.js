const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, queryStringParameters } = event;

    try {
        if (httpMethod === 'GET' && path.includes('/search')) {
            return await searchItems(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function searchItems(event) {
    const query = event.queryStringParameters?.q || '';
    const type = event.queryStringParameters?.type || 'all';

    if (!query) {
        return {
            statusCode: 400,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Query parameter is required' })
        };
    }

    let results = [];

    // Search recipes
    if (type === 'all' || type === 'recipes') {
        const recipeParams = {
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk',
            FilterExpression: 'contains(title, :query) OR contains(description, :query)',
            ExpressionAttributeValues: {
                ':gsi1pk': 'RECIPE',
                ':query': query
            }
        };

        const recipeResult = await dynamodb.query(recipeParams).promise();
        results = results.concat(recipeResult.Items.map(item => ({ ...item, type: 'recipe' })));
    }

    // Search ingredients
    if (type === 'all' || type === 'ingredients') {
        const ingredientParams = {
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk',
            FilterExpression: 'contains(#name, :query)',
            ExpressionAttributeNames: {
                '#name': 'name'
            },
            ExpressionAttributeValues: {
                ':gsi1pk': 'INGREDIENT',
                ':query': query
            }
        };

        const ingredientResult = await dynamodb.query(ingredientParams).promise();
        results = results.concat(ingredientResult.Items.map(item => ({ ...item, type: 'ingredient' })));
    }

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            query,
            type,
            results: results || [],
            count: results.length
        })
    };
}
