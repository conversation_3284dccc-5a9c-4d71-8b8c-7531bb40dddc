const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/reviews')) {
            return await getReviews(event);
        } else if (httpMethod === 'POST' && path.includes('/reviews')) {
            return await createReview(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/review/')) {
            return await updateReview(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/review/')) {
            return await deleteReview(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getReviews(event) {
    const recipeId = event.queryStringParameters?.recipeId || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `RECIPE#${recipeId}`,
            ':sk': 'REVIEW#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            reviews: result.Items || []
        })
    };
}

async function createReview(body, event) {
    const userId = 'user-123';
    const reviewId = `review-${Date.now()}`;
    const { recipeId, rating, comment } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `RECIPE#${recipeId}`,
            SK: `REVIEW#${reviewId}`,
            id: reviewId,
            userId,
            recipeId,
            rating,
            comment,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Review created successfully',
            review: params.Item
        })
    };
}

async function updateReview(body, event) {
    const reviewId = event.pathParameters?.id || 'review-123';
    const { recipeId, rating, comment } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: `REVIEW#${reviewId}`
        },
        UpdateExpression: 'SET rating = :rating, comment = :comment, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':rating': rating,
            ':comment': comment,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Review updated successfully',
            review: result.Attributes
        })
    };
}

async function deleteReview(event) {
    const reviewId = event.pathParameters?.id || 'review-123';
    const recipeId = event.queryStringParameters?.recipeId || 'recipe-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `RECIPE#${recipeId}`,
            SK: `REVIEW#${reviewId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Review deleted successfully'
        })
    };
}
