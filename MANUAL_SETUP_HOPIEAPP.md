# 🚀 HopieApp - Configuración Manual Completa en AWS Console

## 📋 Índice
1. [DynamoDB](#1-dynamodb)
2. [S3 Buckets](#2-s3-buckets)
3. [Cognito User Pool](#3-cognito-user-pool)
4. [Funcion<PERSON>](#4-funciones-lambda)
5. [API Gateway REST](#5-api-gateway-rest)
6. [API Gateway WebSocket](#6-api-gateway-websocket)
7. [CloudFront](#7-cloudfront)
8. [EventBridge](#8-eventbridge)
9. [Configuración Final](#9-configuración-final)

---

## 1. DynamoDB

### 1.1 Crear Tabla Principal
1. Ve a **DynamoDB** → **Tables** → **Create table**
2. **Table name**: `hopie-main-table-dev`
3. **Partition key**:
   - **Attribute name**: `PK`
   - **Type**: String
4. **Sort key**:
   - **Attribute name**: `SK`
   - **Type**: String
5. **Table settings**:
   - **Table class**: Standard
   - **Capacity mode**: On-demand
   - **Encryption**: Owned by Amazon DynamoDB
6. Click **Create table**

### 1.2 Configurar GSIs (Global Secondary Indexes)
**IMPORTANTE**: Espera a que la tabla esté en estado "Active" antes de crear los índices.

Después de crear la tabla, ve a **Indexes** → **Create index**:

**GSI 1:**
- **Index name**: `GSI1`
- **Partition key**:
  - **Attribute name**: `GSI1PK`
  - **Type**: String
- **Sort key**:
  - **Attribute name**: `GSI1SK`
  - **Type**: String
- **Capacity mode**: On-demand
- **Projected attributes**: All attributes
- Click **Create index**

**GSI 2:**
- **Index name**: `GSI2`
- **Partition key**:
  - **Attribute name**: `GSI2PK`
  - **Type**: String
- **Sort key**:
  - **Attribute name**: `GSI2SK`
  - **Type**: String
- **Capacity mode**: On-demand
- **Projected attributes**: All attributes
- Click **Create index**

**GSI 3:**
- **Index name**: `GSI3`
- **Partition key**:
  - **Attribute name**: `GSI3PK`
  - **Type**: String
- **Sort key**:
  - **Attribute name**: `GSI3SK`
  - **Type**: String
- **Capacity mode**: On-demand
- **Projected attributes**: All attributes
- Click **Create index**

### 1.3 Habilitar DynamoDB Streams
1. Ve a la tabla creada → **Exports and streams** → **DynamoDB stream details**
2. Click **Enable**
3. **View type**: Selecciona **New and old images**
4. Click **Enable stream**

### 1.4 Estructura de Datos de la Tabla
**Esta tabla almacenará todos los datos con estos patrones:**

**Usuarios:**
- PK: `USER#123456`
- SK: `PROFILE` | `PREFERENCES` | `STREAK#CURRENT`

**Parejas:**
- PK: `COUPLE#789012`
- SK: `INFO`

**Familia:**
- PK: `USER#123456`
- SK: `FAMILY#member1` | `FAMILY#member2`

**Preguntas:**
- PK: `DAILY_QUESTION`
- SK: `2024-01-15` (fecha)

**Respuestas:**
- PK: `USER#123456`
- SK: `ANSWER#2024-01-15#question1`

**Metas de vida:**
- PK: `USER#123456`
- SK: `GOAL#goal1` | `GOAL#goal2`

**Lugares favoritos:**
- PK: `USER#123456`
- SK: `PLACE#place1` | `PLACE#place2`

**Ubicaciones:**
- PK: `USER#123456`
- SK: `LOCATION#CURRENT` | `LOCATION#timestamp`

**Imágenes:**
- PK: `USER#123456`
- SK: `IMAGE#image1` | `IMAGE#image2`

**Notificaciones:**
- PK: `USER#123456`
- SK: `NOTIFICATION#notif1`

**Chat (WebSocket):**
- PK: `CONNECTION`
- SK: `connectionId`
- PK: `ROOM#room1`
- SK: `MESSAGE#timestamp`

---

## 2. S3 Buckets

### 2.1 Bucket de Assets
1. Ve a **S3** → **Create bucket**
2. **Bucket name**: `hopie-app-assets-dev-123456`
   - **IMPORTANTE**: Reemplaza `123456` con números únicos (ej: tu fecha de nacimiento)
   - **Ejemplo**: `hopie-app-assets-dev-240115` (si es 15 de enero 2024)
3. **AWS Region**: **US East (Ohio) us-east-2**
4. **Object Ownership**:
   - Selecciona **ACLs enabled**
   - **Bucket owner preferred**
5. **Block Public Access settings for this bucket**:
   - ❌ **Desmarcar** "Block all public access"
   - ❌ **Desmarcar** "Block public access to buckets and objects granted through new access control lists (ACLs)"
   - ❌ **Desmarcar** "Block public access to buckets and objects granted through any access control lists (ACLs)"
   - ❌ **Desmarcar** "Block public access to buckets and objects granted through new public bucket or access point policies"
   - ❌ **Desmarcar** "Block public access to buckets and objects granted through any public bucket or access point policies"
   - ✅ **Marcar** "I acknowledge that the current settings might result in this bucket and the objects within becoming public"
6. **Bucket Versioning**: **Enable**
7. **Default encryption**:
   - **Encryption type**: Server-side encryption with Amazon S3 managed keys (SSE-S3)
8. Click **Create bucket**

### 2.2 Configurar CORS para Assets
1. Ve al bucket creado → **Permissions** → **Cross-origin resource sharing (CORS)**
2. Click **Edit**
3. **Borra todo** el contenido existente
4. **Copia y pega exactamente esto**:
```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": ["ETag", "x-amz-meta-custom-header"]
    }
]
```
5. Click **Save changes**

### 2.3 Configurar Bucket Policy
1. Ve al bucket → **Permissions** → **Bucket policy**
2. Click **Edit**
3. **Copia y pega exactamente esto** (reemplaza `TU-BUCKET-NAME` con tu nombre de bucket):
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::TU-BUCKET-NAME/*"
        }
    ]
}
```
4. **IMPORTANTE**: Reemplaza `TU-BUCKET-NAME` con el nombre real de tu bucket
5. Click **Save changes**

### 2.4 Crear Estructura de Carpetas
1. Ve al bucket → **Objects** → **Create folder**
2. Crear estas carpetas:
   - **Folder name**: `users/` → **Create folder**
   - **Folder name**: `images/` → **Create folder**
   - **Folder name**: `thumbnails/` → **Create folder**
   - **Folder name**: `temp/` → **Create folder**

---

## 3. Cognito User Pool

### 3.1 Crear User Pool
1. Ve a **Cognito** → **User pools** → **Create user pool**

**Step 1 - Configure sign-in experience**:
2. **Authentication providers**:
   - ✅ **Cognito user pool**
3. **Cognito user pool sign-in options**:
   - ✅ **Email**
   - ❌ Username (desmarcar)
   - ❌ Phone number (desmarcar)
4. Click **Next**

**Step 2 - Configure security requirements**:
5. **Password policy**:
   - **Password policy mode**: Cognito defaults
6. **Multi-factor authentication**:
   - **MFA enforcement**: No MFA
7. **User account recovery**:
   - ✅ **Enable self-service account recovery**
   - **Delivery method for user account recovery messages**: Email only
8. Click **Next**

**Step 3 - Configure sign-up experience**:
9. **Self-registration**:
   - ✅ **Enable self-registration**
10. **Cognito-assisted verification and confirmation**:
    - **Attributes to verify**: Email
11. **Required attributes**:
    - ✅ **email**
    - ✅ **name**
12. Click **Next**

**Step 4 - Configure message delivery**:
13. **Email**:
    - **Email provider**: Send email with Cognito
14. **SMS**: (dejar por defecto)
15. Click **Next**

**Step 5 - Integrate your app**:
16. **User pool name**: `hopie-user-pool-dev`
17. **Hosted authentication pages**:
    - ✅ **Use the Cognito Hosted UI**
18. **Domain**:
    - **Domain type**: Use a Cognito domain
    - **Cognito domain**: `hopie-app-dev-123456` (usa números únicos)
19. **Initial app client**:
    - **App type**: Public client
    - **App client name**: `hopie-app-client`
    - **Client secret**: ❌ **Don't generate a client secret**
20. Click **Next**

**Step 6 - Review and create**:
21. Revisar toda la configuración
22. Click **Create user pool**

**IMPORTANTE**: Anota estos valores que necesitarás:
- **User Pool ID**: (aparecerá después de crear)
- **App Client ID**: (aparecerá después de crear)
- **Domain**: `hopie-app-dev-123456.auth.us-east-2.amazoncognito.com`

### 3.2 Configurar Google OAuth
1. Ve al User Pool creado → **Sign-in experience** → **Federated identity provider sign-in**
2. Click **Add identity provider**
3. **Provider type**: **Google**
4. **Provider name**: `Google` (dejar por defecto)
5. **Google app ID**:
   ```
   190717108216-lopbdqappft19j6c4c95su1eddjksvnd.apps.googleusercontent.com
   ```
6. **Google app secret**:
   ```
   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
   ```
7. **Authorized scopes**:
   ```
   email openid profile
   ```
8. **Map attributes**: (dejar por defecto)
9. Click **Add identity provider**

### 3.3 Configurar App Client
1. Ve a **App integration** → **App clients and analytics**
2. Click en tu app client `hopie-app-client`
3. Click **Edit** en **Hosted UI**
4. **Allowed callback URLs**:
   ```
   https://hopieapp.com/callback,http://localhost:3000/callback
   ```
5. **Allowed sign-out URLs**:
   ```
   https://hopieapp.com/signout,http://localhost:3000/signout
   ```
6. **Identity providers**:
   - ✅ **Cognito user pool**
   - ✅ **Google**
7. **OAuth 2.0 grant types**:
   - ✅ **Authorization code grant**
   - ✅ **Implicit grant**
8. **OpenID Connect scopes**:
   - ✅ **email**
   - ✅ **openid**
   - ✅ **profile**
9. **Custom scopes**: (dejar vacío)
10. Click **Save changes**

### 3.4 Obtener Información Importante
**Después de crear todo, anota estos valores:**

1. Ve a **General settings**:
   - **User Pool ID**: `us-east-2_XXXXXXXXX` ← **ANOTA ESTO**

2. Ve a **App integration** → **App clients**:
   - **Client ID**: `abcdef123456789` ← **ANOTA ESTO**

3. **Domain URL**:
   - `https://hopie-app-dev-123456.auth.us-east-2.amazoncognito.com` ← **ANOTA ESTO**

**Estos valores los necesitarás para las funciones Lambda.**

---

## 4. Funciones Lambda

### 4.1 Crear Rol IAM para Lambda
1. Ve a **IAM** → **Roles** → **Create role**
2. **Trusted entity type**: **AWS service**
3. **Use case**:
   - **Service**: **Lambda**
   - Click **Next**
4. **Add permissions**:
   - Buscar y seleccionar: ✅ **AWSLambdaBasicExecutionRole**
   - Buscar y seleccionar: ✅ **AmazonDynamoDBFullAccess**
   - Buscar y seleccionar: ✅ **AmazonS3FullAccess**
   - Buscar y seleccionar: ✅ **AmazonCognitoPowerUser**
   - Click **Next**
5. **Role details**:
   - **Role name**: `hopie-lambda-execution-role`
   - **Description**: `Execution role for HopieApp Lambda functions`
6. Click **Create role**

**IMPORTANTE**: Anota el **Role ARN** que aparecerá:
- **Role ARN**: `arn:aws:iam::ACCOUNT-ID:role/hopie-lambda-execution-role` ← **ANOTA ESTO**

### 4.2 Función 1: AuthFunction
1. Ve a **Lambda** → **Create function**
2. **Author from scratch**
3. **Basic information**:
   - **Function name**: `hopie-auth-dev`
   - **Runtime**: **Node.js 18.x**
   - **Architecture**: **x86_64**
4. **Change default execution role**:
   - **Execution role**: **Use an existing role**
   - **Existing role**: `hopie-lambda-execution-role`
5. Click **Create function**

6. **Configurar el código**:
   - Ve a **Code** → **Code source**
   - **Borra todo** el código existente en `index.js`
   - **Copia y pega exactamente este código**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'POST' && path.includes('/login')) {
            return await handleLogin(requestBody);
        } else if (httpMethod === 'POST' && path.includes('/register')) {
            return await handleRegister(requestBody);
        } else if (httpMethod === 'POST' && path.includes('/refresh')) {
            return await handleRefreshToken(requestBody);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function handleLogin(body) {
    const { email, password } = body;
    
    // Aquí implementarías la lógica de login con Cognito
    // Por ahora retornamos un token mock
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Login successful',
            token: 'mock-jwt-token',
            user: { email, id: 'user-123' }
        })
    };
}

async function handleRegister(body) {
    const { email, password, name } = body;
    
    // Aquí implementarías la lógica de registro con Cognito
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'User registered successfully',
            user: { email, name, id: 'user-new-123' }
        })
    };
}

async function handleRefreshToken(body) {
    const { refreshToken } = body;
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Token refreshed',
            token: 'new-mock-jwt-token'
        })
    };
}
```

7. **Configurar variables de entorno**:
   - Ve a **Configuration** → **Environment variables**
   - Click **Edit**
   - **Add environment variable**:
     - **Key**: `TABLE_NAME`
     - **Value**: `hopie-main-table-dev`
   - **Add environment variable**:
     - **Key**: `USER_POOL_ID`
     - **Value**: `us-east-2_XXXXXXXXX` (el que anotaste de Cognito)
   - **Add environment variable**:
     - **Key**: `CLIENT_ID`
     - **Value**: `abcdef123456789` (el que anotaste de Cognito)
   - **Add environment variable**:
     - **Key**: `REGION`
     - **Value**: `us-east-2`
   - Click **Save**

8. **Configurar timeout**:
   - Ve a **Configuration** → **General configuration**
   - Click **Edit**
   - **Timeout**: `30` seconds
   - **Memory**: `256` MB
   - Click **Save**

9. **Deploy el código**:
   - Ve a **Code** → **Code source**
   - Click **Deploy**
   - Espera a que aparezca "Changes deployed"

### 4.3 Función 2: UserFunction
1. Ve a **Lambda** → **Create function**
2. **Author from scratch**
3. **Basic information**:
   - **Function name**: `hopie-user-dev`
   - **Runtime**: **Node.js 18.x**
   - **Architecture**: **x86_64**
4. **Change default execution role**:
   - **Execution role**: **Use an existing role**
   - **Existing role**: `hopie-lambda-execution-role`
5. Click **Create function**

6. **Configurar el código**:
   - Ve a **Code** → **Code source**
   - **Borra todo** el código existente en `index.js`
   - **Copia y pega exactamente este código**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body, pathParameters } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/profile')) {
            return await getUserProfile(event);
        } else if (httpMethod === 'PUT' && path.includes('/profile')) {
            return await updateUserProfile(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/preferences')) {
            return await getUserPreferences(event);
        } else if (httpMethod === 'PUT' && path.includes('/preferences')) {
            return await updateUserPreferences(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUserProfile(event) {
    // Extraer userId del token JWT (mock por ahora)
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            user: result.Item || { id: userId, name: 'Usuario Demo', email: '<EMAIL>' }
        })
    };
}

async function updateUserProfile(body, event) {
    const userId = 'user-123';
    const { name, email, phone, birthDate } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        },
        UpdateExpression: 'SET #name = :name, email = :email, phone = :phone, birthDate = :birthDate, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':email': email,
            ':phone': phone,
            ':birthDate': birthDate,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Profile updated successfully',
            user: result.Attributes
        })
    };
}

async function getUserPreferences(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            preferences: result.Item || {
                notifications: true,
                theme: 'light',
                language: 'es'
            }
        })
    };
}

async function updateUserPreferences(body, event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        },
        UpdateExpression: 'SET preferences = :preferences, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':preferences': body,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Preferences updated successfully',
            preferences: result.Attributes
        })
    };
}
```

7. **Configurar variables de entorno**:
   - Ve a **Configuration** → **Environment variables**
   - Click **Edit**
   - **Add environment variable**:
     - **Key**: `TABLE_NAME`
     - **Value**: `hopie-main-table-dev`
   - Click **Save**

8. **Configurar timeout**:
   - Ve a **Configuration** → **General configuration**
   - Click **Edit**
   - **Timeout**: `30` seconds
   - **Memory**: `256` MB
   - Click **Save**

9. **Deploy el código**:
   - Ve a **Code** → **Code source**
   - Click **Deploy**
   - Espera a que aparezca "Changes deployed"

**IMPORTANTE**: Repite estos mismos pasos (crear función, código, variables, timeout, deploy) para TODAS las funciones restantes. Solo cambia:
- El **nombre de la función**
- El **código específico** de cada función
- Las **variables de entorno** específicas (si las hay)

### 4.4 Lista de TODAS las funciones que debes crear:

**Crea estas 12 funciones más siguiendo el mismo proceso:**

1. ✅ `hopie-auth-dev` (ya creada)
2. ✅ `hopie-user-dev` (ya creada)
3. `hopie-couple-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART2.md
4. `hopie-tree-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART2.md
5. `hopie-questions-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART2.md
6. `hopie-lifeplan-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART2.md
7. `hopie-places-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART2.md
8. `hopie-location-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART2.md
9. `hopie-stats-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART2.md
10. `hopie-image-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART3.md
11. `hopie-notification-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART3.md
12. `hopie-chat-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART3.md
13. `hopie-image-processor-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART3.md
14. `hopie-scheduler-dev` → Código en MANUAL_SETUP_HOPIEAPP_PART3.md

**Variables de entorno para cada función:**
- **Todas las funciones**: `TABLE_NAME` = `hopie-main-table-dev`
- **hopie-image-dev**: Agregar también `BUCKET_NAME` = `hopie-app-assets-dev-123456`
- **hopie-image-processor-dev**: Agregar también `BUCKET_NAME` = `hopie-app-assets-dev-123456`

---

## 5. API Gateway REST - CONFIGURACIÓN COMPLETA PASO A PASO

### 5.1 Crear API Gateway REST
1. Ve a **AWS Console** → Busca **API Gateway** → Click **API Gateway**
2. En la página principal, click **Create API**
3. **REST API** (la primera opción) → Click **Build**
4. **Create new API**:
   - ✅ **New API** (seleccionado por defecto)
   - ❌ **Clone from existing API**
   - ❌ **Import from Swagger or Open API 3**
5. **Settings**:
   - **API name**: `hopie-api-dev`
   - **Description**: `HopieApp REST API for mobile application`
   - **Endpoint Type**: **Regional** (seleccionado por defecto)
6. Click **Create API**

### 5.2 Crear Authorizer de Cognito
1. En el panel izquierdo, click **Authorizers**
2. Click **Create New Authorizer**
3. **Create Authorizer**:
   - **Name**: `CognitoAuthorizer`
   - **Type**: **Cognito**
4. **Cognito User Pool**:
   - **Cognito User Pool**: Selecciona `hopie-user-pool-dev` (el que creaste)
   - **Token Source**: `Authorization`
   - **Token Validation**: (dejar vacío)
5. Click **Create**
6. **Test the authorizer** (opcional):
   - Puedes probar más tarde cuando tengas tokens
7. El authorizer aparecerá en la lista

### 5.3 Crear Recursos y Métodos - PROCESO COMPLETO

#### 5.3.1 Recurso /auth (SIN AUTORIZACIÓN)

**Paso 1: Crear recurso /auth**
1. En el panel izquierdo, click **Resources**
2. Selecciona **/** (root)
3. Click **Actions** → **Create Resource**
4. **New Child Resource**:
   - **Configure as proxy resource**: ❌ (NO marcar)
   - **Resource Name**: `auth`
   - **Resource Path**: `/auth` (se llena automáticamente)
   - **Enable API Gateway CORS**: ✅ (SÍ marcar)
5. Click **Create Resource**

**Paso 2: Crear recurso {proxy+} bajo /auth**
1. Selecciona `/auth` en el árbol de recursos
2. Click **Actions** → **Create Resource**
3. **New Child Resource**:
   - **Configure as proxy resource**: ✅ (SÍ marcar)
   - **Resource Name**: `proxy` (se llena automáticamente)
   - **Resource Path**: `/{proxy+}` (se llena automáticamente)
   - **Enable API Gateway CORS**: ✅ (SÍ marcar)
4. Click **Create Resource**

**Paso 3: Crear método ANY en /{proxy+}**
1. Selecciona `/{proxy+}` bajo `/auth`
2. Click **Actions** → **Create Method**
3. En el dropdown que aparece, selecciona **ANY**
4. Click el ✓ (checkmark)
5. **Setup**:
   - **Integration type**: **Lambda Function**
   - **Use Lambda Proxy integration**: ✅ (SÍ marcar)
   - **Lambda Region**: **us-east-2**
   - **Lambda Function**: `hopie-auth-dev`
   - **Use Default Timeout**: ✅ (SÍ marcar)
6. Click **Save**
7. **Add Permission to Lambda Function**: Click **OK**

**Paso 4: Configurar Method Request**
1. Con `/{proxy+}` seleccionado, click en **ANY**
2. Click **Method Request**
3. **Settings**:
   - **Authorization**: **NONE** (muy importante para auth)
   - **Request Validator**: **None**
   - **API Key Required**: **false**
4. No cambies nada más, click **← Method Execution** para volver

#### 5.3.2 Recurso /users (CON AUTORIZACIÓN)

**Paso 1: Crear recurso /users**
1. Selecciona **/** (root)
2. Click **Actions** → **Create Resource**
3. **New Child Resource**:
   - **Configure as proxy resource**: ❌ (NO marcar)
   - **Resource Name**: `users`
   - **Resource Path**: `/users`
   - **Enable API Gateway CORS**: ✅ (SÍ marcar)
4. Click **Create Resource**

**Paso 2: Crear recurso {proxy+} bajo /users**
1. Selecciona `/users`
2. Click **Actions** → **Create Resource**
3. **New Child Resource**:
   - **Configure as proxy resource**: ✅ (SÍ marcar)
   - **Resource Name**: `proxy`
   - **Resource Path**: `/{proxy+}`
   - **Enable API Gateway CORS**: ✅ (SÍ marcar)
4. Click **Create Resource**

**Paso 3: Crear método ANY en /{proxy+}**
1. Selecciona `/{proxy+}` bajo `/users`
2. Click **Actions** → **Create Method**
3. Selecciona **ANY** → Click ✓
4. **Setup**:
   - **Integration type**: **Lambda Function**
   - **Use Lambda Proxy integration**: ✅ (SÍ marcar)
   - **Lambda Region**: **us-east-2**
   - **Lambda Function**: `hopie-user-dev`
   - **Use Default Timeout**: ✅ (SÍ marcar)
5. Click **Save**
6. Click **OK** para dar permisos

**Paso 4: Configurar Method Request CON AUTORIZACIÓN**
1. Con `/{proxy+}` bajo `/users` seleccionado, click **ANY**
2. Click **Method Request**
3. **Settings**:
   - **Authorization**: **CognitoAuthorizer** (selecciona el que creaste)
   - **Request Validator**: **None**
   - **API Key Required**: **false**
4. Click **← Method Execution** para volver

#### 5.3.3 REPETIR PARA TODOS LOS RECURSOS RESTANTES

**IMPORTANTE**: Repite EXACTAMENTE el mismo proceso para estos recursos:

| Recurso | Lambda Function | Authorization |
|---------|----------------|---------------|
| `/couples` | `hopie-couple-dev` | **CognitoAuthorizer** |
| `/trees` | `hopie-tree-dev` | **CognitoAuthorizer** |
| `/questions` | `hopie-questions-dev` | **CognitoAuthorizer** |
| `/lifeplan` | `hopie-lifeplan-dev` | **CognitoAuthorizer** |
| `/places` | `hopie-places-dev` | **CognitoAuthorizer** |
| `/location` | `hopie-location-dev` | **CognitoAuthorizer** |
| `/stats` | `hopie-stats-dev` | **CognitoAuthorizer** |
| `/images` | `hopie-image-dev` | **CognitoAuthorizer** |
| `/notifications` | `hopie-notification-dev` | **CognitoAuthorizer** |

**Para cada uno:**
1. Crear recurso principal (ej: `/couples`)
2. Crear `/{proxy+}` bajo el recurso
3. Crear método **ANY** en `/{proxy+}`
4. Configurar integración con la Lambda correspondiente
5. Configurar **Authorization** = **CognitoAuthorizer** (excepto `/auth`)

### 5.4 Configurar CORS para TODOS los recursos

**IMPORTANTE**: Haz esto para CADA recurso `/{proxy+}` que creaste:

1. Selecciona `/{proxy+}` (ej: bajo `/auth`)
2. Click **Actions** → **Enable CORS**
3. **Enable CORS**:
   - **Access-Control-Allow-Origin**: `*`
   - **Access-Control-Allow-Headers**:
     ```
     Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent
     ```
   - **Access-Control-Allow-Methods**:
     - ✅ **GET**
     - ✅ **POST**
     - ✅ **PUT**
     - ✅ **DELETE**
     - ✅ **HEAD**
     - ✅ **OPTIONS**
     - ✅ **PATCH**
4. Click **Enable CORS and replace existing CORS headers**
5. Click **Yes, replace existing values**

**Repite esto para TODOS los 11 recursos `/{proxy+}`.**

### 5.5 Desplegar API

1. Selecciona **/** (root) en Resources
2. Click **Actions** → **Deploy API**
3. **Deploy API**:
   - **Deployment stage**: **[New Stage]**
   - **Stage name**: `dev`
   - **Stage description**: `Development stage for HopieApp`
   - **Deployment description**: `Initial deployment with all endpoints`
4. Click **Deploy**

### 5.6 Obtener URL del API

1. Después del deploy, ve a **Stages** en el panel izquierdo
2. Click **dev**
3. En la parte superior verás:
   - **Invoke URL**: `https://XXXXXXXXXX.execute-api.us-east-2.amazonaws.com/dev`
4. **ANOTA ESTA URL** - la necesitarás para Flutter

### 5.7 Verificar que TODO esté configurado

**Verifica que tienes estos recursos:**
```
/
├── /auth
│   └── /{proxy+} → ANY → hopie-auth-dev (NO AUTH)
├── /users
│   └── /{proxy+} → ANY → hopie-user-dev (AUTH)
├── /couples
│   └── /{proxy+} → ANY → hopie-couple-dev (AUTH)
├── /trees
│   └── /{proxy+} → ANY → hopie-tree-dev (AUTH)
├── /questions
│   └── /{proxy+} → ANY → hopie-questions-dev (AUTH)
├── /lifeplan
│   └── /{proxy+} → ANY → hopie-lifeplan-dev (AUTH)
├── /places
│   └── /{proxy+} → ANY → hopie-places-dev (AUTH)
├── /location
│   └── /{proxy+} → ANY → hopie-location-dev (AUTH)
├── /stats
│   └── /{proxy+} → ANY → hopie-stats-dev (AUTH)
├── /images
│   └── /{proxy+} → ANY → hopie-image-dev (AUTH)
└── /notifications
    └── /{proxy+} → ANY → hopie-notification-dev (AUTH)
```

**Tu API estará disponible en:**
```
https://XXXXXXXXXX.execute-api.us-east-2.amazonaws.com/dev
```

### 4.4 Función 3: CoupleFunction
1. **Function name**: `hopie-couple-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/create')) {
            return await createCouple(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/info')) {
            return await getCoupleInfo(event);
        } else if (httpMethod === 'PUT' && path.includes('/update')) {
            return await updateCouple(requestBody, event);
        } else if (httpMethod === 'POST' && path.includes('/invite')) {
            return await invitePartner(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function createCouple(body, event) {
    const userId = 'user-123'; // Extraer del token
    const { partnerEmail, relationshipStart } = body;
    const coupleId = `COUPLE#${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: coupleId,
            SK: 'INFO',
            user1: userId,
            user2: null,
            partnerEmail,
            relationshipStart,
            status: 'pending',
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple created successfully',
            coupleId,
            status: 'pending'
        })
    };
}

async function getCoupleInfo(event) {
    const userId = 'user-123';

    // Buscar pareja del usuario
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :pk',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            couple: result.Items[0] || null
        })
    };
}

async function updateCouple(body, event) {
    const { coupleId, relationshipStart, anniversary } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: coupleId,
            SK: 'INFO'
        },
        UpdateExpression: 'SET relationshipStart = :start, anniversary = :anniversary, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':start': relationshipStart,
            ':anniversary': anniversary,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple updated successfully',
            couple: result.Attributes
        })
    };
}

async function invitePartner(body, event) {
    const { email } = body;

    // Aquí implementarías el envío de invitación por email

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Invitation sent successfully',
            email
        })
    };
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`

### 4.5 Función 4: TreeFunction
1. **Function name**: `hopie-tree-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/family')) {
            return await getFamilyTree(event);
        } else if (httpMethod === 'POST' && path.includes('/add-member')) {
            return await addFamilyMember(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/update-member')) {
            return await updateFamilyMember(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/remove-member')) {
            return await removeFamilyMember(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getFamilyTree(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'FAMILY#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            familyMembers: result.Items || []
        })
    };
}

async function addFamilyMember(body, event) {
    const userId = 'user-123';
    const { name, relationship, birthDate, photo } = body;
    const memberId = `FAMILY#${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: memberId,
            name,
            relationship,
            birthDate,
            photo,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Family member added successfully',
            member: { id: memberId, name, relationship }
        })
    };
}

async function updateFamilyMember(body, event) {
    const userId = 'user-123';
    const { memberId, name, relationship, birthDate, photo } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: memberId
        },
        UpdateExpression: 'SET #name = :name, relationship = :relationship, birthDate = :birthDate, photo = :photo, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':relationship': relationship,
            ':birthDate': birthDate,
            ':photo': photo,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Family member updated successfully',
            member: result.Attributes
        })
    };
}

async function removeFamilyMember(body, event) {
    const userId = 'user-123';
    const { memberId } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: memberId
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Family member removed successfully'
        })
    };
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`

### 4.6 Función 5: QuestionsFunction
1. **Function name**: `hopie-questions-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/daily')) {
            return await getDailyQuestion(event);
        } else if (httpMethod === 'POST' && path.includes('/answer')) {
            return await submitAnswer(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/history')) {
            return await getQuestionHistory(event);
        } else if (httpMethod === 'GET' && path.includes('/categories')) {
            return await getQuestionCategories(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getDailyQuestion(event) {
    const today = new Date().toISOString().split('T')[0];

    // Buscar pregunta del día
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: 'DAILY_QUESTION',
            SK: today
        }
    };

    let result = await dynamodb.get(params).promise();

    if (!result.Item) {
        // Si no hay pregunta para hoy, crear una nueva
        const questions = [
            "¿Cuál es tu recuerdo favorito de nuestra relación?",
            "¿Qué es lo que más admiras de tu pareja?",
            "¿Cuál sería tu cita ideal?",
            "¿Qué meta te gustaría lograr juntos este año?",
            "¿Cuál es tu tradición familiar favorita?"
        ];

        const randomQuestion = questions[Math.floor(Math.random() * questions.length)];

        const newQuestion = {
            PK: 'DAILY_QUESTION',
            SK: today,
            question: randomQuestion,
            category: 'relationship',
            createdAt: new Date().toISOString()
        };

        await dynamodb.put({ TableName: TABLE_NAME, Item: newQuestion }).promise();
        result.Item = newQuestion;
    }

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            question: result.Item
        })
    };
}

async function submitAnswer(body, event) {
    const userId = 'user-123';
    const { questionId, answer, date } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `ANSWER#${questionId}#${date}`,
            questionId,
            answer,
            date,
            submittedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Answer submitted successfully'
        })
    };
}

async function getQuestionHistory(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'ANSWER#'
        },
        ScanIndexForward: false,
        Limit: 30
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            answers: result.Items || []
        })
    };
}

async function getQuestionCategories(event) {
    const categories = [
        { id: 'relationship', name: 'Relación', icon: '💕' },
        { id: 'family', name: 'Familia', icon: '👨‍👩‍👧‍👦' },
        { id: 'dreams', name: 'Sueños', icon: '✨' },
        { id: 'memories', name: 'Recuerdos', icon: '📸' },
        { id: 'future', name: 'Futuro', icon: '🚀' }
    ];

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            categories
        })
    };
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`

### 4.7 Función 6: LifePlanFunction
1. **Function name**: `hopie-lifeplan-dev`
2. **Runtime**: Node.js 18.x
3. **Execution role**: `hopie-lambda-execution-role`
4. **Code**:
```javascript
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/goals')) {
            return await getLifeGoals(event);
        } else if (httpMethod === 'POST' && path.includes('/create')) {
            return await createLifeGoal(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/update')) {
            return await updateLifeGoal(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/delete')) {
            return await deleteLifeGoal(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getLifeGoals(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'GOAL#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            goals: result.Items || []
        })
    };
}

async function createLifeGoal(body, event) {
    const userId = 'user-123';
    const { title, description, category, targetDate, priority } = body;
    const goalId = `GOAL#${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: goalId,
            title,
            description,
            category,
            targetDate,
            priority,
            status: 'active',
            progress: 0,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life goal created successfully',
            goalId
        })
    };
}

async function updateLifeGoal(body, event) {
    const userId = 'user-123';
    const { goalId, title, description, progress, status } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: goalId
        },
        UpdateExpression: 'SET title = :title, description = :description, progress = :progress, #status = :status, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#status': 'status'
        },
        ExpressionAttributeValues: {
            ':title': title,
            ':description': description,
            ':progress': progress,
            ':status': status,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life goal updated successfully',
            goal: result.Attributes
        })
    };
}

async function deleteLifeGoal(body, event) {
    const userId = 'user-123';
    const { goalId } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: goalId
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life goal deleted successfully'
        })
    };
}
```

5. **Environment variables**:
   - `TABLE_NAME`: `hopie-main-table-dev`
