const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');

/**
 * Location Service - Handle location sharing and tracking
 */

exports.handler = async(event, context) => {
  console.log('Location service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;

    switch (route) {
    case 'POST /location/share':
      return await handleShareLocation(user, body);

    case 'GET /location/current':
      return await handleGetCurrentLocation(user);

    case 'GET /location/history':
      return await handleGetLocationHistory(user);

    case 'PUT /location/settings':
      return await handleUpdateLocationSettings(user, body);

    default:
      return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Location service error:', error);
    return response.handleError(error);
  }
};

async function handleShareLocation(user, body) {
  // Implementation for sharing location
  return response.success({ message: 'Location shared successfully' });
}

async function handleGetCurrentLocation(user) {
  // Implementation for getting current location
  return response.success({ location: null });
}

async function handleGetLocationHistory(user) {
  // Implementation for getting location history
  return response.success({ history: [] });
}

async function handleUpdateLocationSettings(user, body) {
  // Implementation for updating location settings
  return response.success({ message: 'Location settings updated successfully' });
}
