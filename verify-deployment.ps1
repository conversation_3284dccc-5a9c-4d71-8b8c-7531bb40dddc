param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage = "dev"
)

# Colores para output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput $Blue "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "❌ $message"
}

function Test-Command($command) {
    try {
        & $command --version 2>$null | Out-Null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Test-AWSCredentials() {
    try {
        $identity = aws sts get-caller-identity --output json 2>$null | ConvertFrom-Json
        return $identity -ne $null
    } catch {
        return $false
    }
}

function Test-FileExists($path, $description) {
    if (Test-Path $path) {
        Write-Success "$description encontrado: $path"
        return $true
    } else {
        Write-Error "$description no encontrado: $path"
        return $false
    }
}

function Test-DirectoryStructure() {
    $requiredDirs = @(
        "src/auth",
        "src/user", 
        "src/couple",
        "src/tree",
        "src/questions",
        "src/lifeplan",
        "src/places",
        "src/chat",
        "src/location",
        "src/stats",
        "src/image",
        "src/image-processor",
        "src/notification",
        "src/scheduler"
    )
    
    $allExist = $true
    foreach ($dir in $requiredDirs) {
        if (Test-Path $dir) {
            Write-Success "Directorio encontrado: $dir"
        } else {
            Write-Warning "Directorio faltante: $dir"
            $allExist = $false
        }
    }
    
    return $allExist
}

function Test-LambdaFunctions() {
    $requiredFiles = @(
        "src/auth/index.js",
        "src/auth/package.json",
        "src/user/index.js",
        "src/user/package.json",
        "src/couple/index.js",
        "src/couple/package.json",
        "src/tree/index.js",
        "src/tree/package.json",
        "src/questions/index.js",
        "src/questions/package.json",
        "src/lifeplan/index.js",
        "src/lifeplan/package.json",
        "src/places/index.js",
        "src/places/package.json",
        "src/chat/index.js",
        "src/chat/package.json",
        "src/location/index.js",
        "src/location/package.json",
        "src/stats/index.js",
        "src/stats/package.json",
        "src/image/index.js",
        "src/image/package.json",
        "src/image-processor/index.js",
        "src/image-processor/package.json",
        "src/notification/index.js",
        "src/notification/package.json",
        "src/scheduler/index.js",
        "src/scheduler/package.json"
    )
    
    $allExist = $true
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Success "Archivo Lambda encontrado: $file"
        } else {
            Write-Warning "Archivo Lambda faltante: $file"
            $allExist = $false
        }
    }
    
    return $allExist
}

Write-Info "🔍 Verificando preparación para despliegue de HopieApp"
Write-Info "Stage objetivo: $Stage"
Write-Info "=" * 50

$allChecks = $true

# 1. Verificar herramientas CLI
Write-Info "1. Verificando herramientas CLI..."

if (Test-Command "aws") {
    Write-Success "AWS CLI instalado"
} else {
    Write-Error "AWS CLI no encontrado"
    $allChecks = $false
}

if (Test-Command "sam") {
    $samVersion = sam --version
    Write-Success "SAM CLI instalado: $samVersion"
} else {
    Write-Error "SAM CLI no encontrado"
    $allChecks = $false
}

# 2. Verificar credenciales AWS
Write-Info "`n2. Verificando credenciales AWS..."

if (Test-AWSCredentials) {
    $identity = aws sts get-caller-identity --output json | ConvertFrom-Json
    Write-Success "Credenciales AWS válidas"
    Write-Info "   Account: $($identity.Account)"
    Write-Info "   User: $($identity.Arn)"
} else {
    Write-Error "Credenciales AWS no válidas o no configuradas"
    $allChecks = $false
}

# 3. Verificar archivos de template
Write-Info "`n3. Verificando archivos de template..."

$templateChecks = @(
    @{ Path = "template.yaml"; Description = "Template principal" },
    @{ Path = "template-base.yaml"; Description = "Template base" },
    @{ Path = "deploy-config.json"; Description = "Configuración de despliegue" }
)

foreach ($check in $templateChecks) {
    if (-not (Test-FileExists $check.Path $check.Description)) {
        $allChecks = $false
    }
}

# 4. Verificar scripts de despliegue
Write-Info "`n4. Verificando scripts de despliegue..."

$scriptChecks = @(
    @{ Path = "deploy-safe.ps1"; Description = "Script de despliegue seguro" },
    @{ Path = "deploy-phased.ps1"; Description = "Script de despliegue por fases" },
    @{ Path = "deploy.ps1"; Description = "Script de despliegue estándar" }
)

foreach ($check in $scriptChecks) {
    if (-not (Test-FileExists $check.Path $check.Description)) {
        $allChecks = $false
    }
}

# 5. Verificar estructura de directorios
Write-Info "`n5. Verificando estructura de directorios..."

if (-not (Test-DirectoryStructure)) {
    Write-Warning "Algunos directorios de funciones Lambda están faltando"
    Write-Info "Esto puede causar errores durante el despliegue"
}

# 6. Verificar archivos de funciones Lambda
Write-Info "`n6. Verificando archivos de funciones Lambda..."

if (-not (Test-LambdaFunctions)) {
    Write-Warning "Algunos archivos de funciones Lambda están faltando"
    Write-Info "Considera usar -SkipLambdas en el despliegue inicial"
}

# 7. Verificar configuración del stage
Write-Info "`n7. Verificando configuración del stage..."

try {
    $config = Get-Content "deploy-config.json" | ConvertFrom-Json
    $stageConfig = $config.$Stage
    
    if ($stageConfig) {
        Write-Success "Configuración encontrada para stage: $Stage"
        Write-Info "   Domain Prefix: $($stageConfig.cognitoDomainPrefix)"
        Write-Info "   Lambda Functions: $($stageConfig.enableLambdaFunctions)"
        Write-Info "   WebSocket: $($stageConfig.enableWebSocket)"
        Write-Info "   Deployment Strategy: $($stageConfig.deploymentStrategy)"
    } else {
        Write-Error "Configuración no encontrada para stage: $Stage"
        $allChecks = $false
    }
} catch {
    Write-Error "Error leyendo configuración: $_"
    $allChecks = $false
}

# Resumen final
Write-Info "`n" + "=" * 50
if ($allChecks) {
    Write-Success "🎉 ¡Verificación completada! Todo listo para el despliegue"
    Write-Info "`nComandos recomendados:"
    Write-ColorOutput $Blue "   # Despliegue seguro (recomendado)"
    Write-ColorOutput $Blue "   .\deploy-safe.ps1 -Stage $Stage"
    Write-ColorOutput $Blue "`n   # Solo infraestructura base"
    Write-ColorOutput $Blue "   .\deploy-safe.ps1 -Stage $Stage -BaseOnly"
    Write-ColorOutput $Blue "`n   # Despliegue por fases"
    Write-ColorOutput $Blue "   .\deploy-phased.ps1 -Stage $Stage"
} else {
    Write-Error "❌ Verificación falló. Revisa los errores arriba antes de desplegar"
    Write-Info "`nPasos para resolver:"
    Write-Info "1. Instala las herramientas faltantes (AWS CLI, SAM CLI)"
    Write-Info "2. Configura credenciales AWS: aws configure"
    Write-Info "3. Crea los archivos/directorios faltantes"
    Write-Info "4. Ejecuta este script nuevamente"
}

Write-Info "`nPara más información, consulta: DEPLOYMENT-README.md"
