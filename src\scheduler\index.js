const database = require('../shared/database');
const { createDailyQuestion } = require('../questions/index');

/**
 * Scheduler Service - Handle scheduled tasks like daily questions and streak calculations
 */

exports.handler = async(event, context) => {
  console.log('Scheduler service event:', JSON.stringify(event, null, 2));

  try {
    const { action } = event;

    switch (action) {
    case 'createDailyQuestion':
      return await handleCreateDailyQuestion();

    case 'calculateStreaks':
      return await handleCalculateStreaks();

    default:
      console.log('Unknown scheduler action:', action);
      return { statusCode: 400, body: 'Unknown action' };
    }
  } catch (error) {
    console.error('Scheduler service error:', error);
    return { statusCode: 500, body: 'Scheduler error' };
  }
};

/**
 * Create daily question
 */
async function handleCreateDailyQuestion() {
  try {
    const today = new Date().toISOString().split('T')[0];

    // Check if question already exists for today
    const existingQuestion = await database.get(`DAILY_QUESTION#${today}`, 'QUESTION');

    if (existingQuestion) {
      console.log('Question already exists for today:', today);
      return { statusCode: 200, body: 'Question already exists' };
    }

    // Create new question
    const question = await createDailyQuestion(today);

    console.log('Created daily question:', question);
    return { statusCode: 200, body: 'Daily question created successfully' };
  } catch (error) {
    console.error('Create daily question error:', error);
    throw error;
  }
}

/**
 * Calculate user streaks
 */
async function handleCalculateStreaks() {
  try {
    const today = new Date();
    const currentMonth = today.toISOString().substring(0, 7); // YYYY-MM

    // Get all active couples
    const couples = await database.queryGSI1('TYPE#COUPLE');

    for (const couple of couples.items) {
      if (couple.status !== 'active') {
        continue;
      }

      await calculateCoupleStreaks(couple, currentMonth);
    }

    console.log(`Calculated streaks for ${couples.items.length} couples`);
    return { statusCode: 200, body: 'Streaks calculated successfully' };
  } catch (error) {
    console.error('Calculate streaks error:', error);
    throw error;
  }
}

/**
 * Calculate streaks for a specific couple
 */
async function calculateCoupleStreaks(couple, currentMonth) {
  try {
    const { coupleId, user1Id, user2Id } = couple;

    // Calculate individual user streaks
    await calculateUserStreak(user1Id, currentMonth);
    await calculateUserStreak(user2Id, currentMonth);

    // Calculate couple streak (both users active)
    await calculateCoupleStreak(coupleId, currentMonth);

  } catch (error) {
    console.error('Calculate couple streaks error:', error);
  }
}

/**
 * Calculate individual user streak
 */
async function calculateUserStreak(userId, currentMonth) {
  try {
    // Get current streak record
    let streakRecord = await database.get(`USER#${userId}`, `STREAK#${currentMonth}`);

    if (!streakRecord) {
      streakRecord = {
        PK: `USER#${userId}`,
        SK: `STREAK#${currentMonth}`,
        userId,
        month: currentMonth,
        currentStreak: 0,
        longestStreak: 0,
        lastActivityDate: null,
        activeDays: [],
        totalActiveDays: 0,
        GSI1PK: 'TYPE#USER_STREAK',
        GSI1SK: `MONTH#${currentMonth}#${userId}`
      };
    }

    // Check activity for the last 30 days
    const today = new Date();
    const activeDays = [];
    let currentStreak = 0;
    let longestStreak = streakRecord.longestStreak || 0;

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      // Check if user was active on this date (watered tree or answered question)
      const wasActive = await checkUserActivityForDate(userId, dateStr);

      if (wasActive) {
        activeDays.push(dateStr);

        if (i === 0) { // Today or most recent day
          currentStreak = 1;

          // Count consecutive days
          for (let j = 1; j < 30; j++) {
            const prevDate = new Date(today);
            prevDate.setDate(prevDate.getDate() - j);
            const prevDateStr = prevDate.toISOString().split('T')[0];

            const wasPrevActive = await checkUserActivityForDate(userId, prevDateStr);
            if (wasPrevActive) {
              currentStreak++;
            } else {
              break;
            }
          }
        }
      } else if (i === 0) {
        currentStreak = 0;
      }
    }

    longestStreak = Math.max(longestStreak, currentStreak);

    // Update streak record
    const updatedStreak = {
      ...streakRecord,
      currentStreak,
      longestStreak,
      lastActivityDate: activeDays.length > 0 ? activeDays[0] : streakRecord.lastActivityDate,
      activeDays: activeDays.reverse(), // Oldest first
      totalActiveDays: activeDays.length,
      updatedAt: new Date().toISOString()
    };

    await database.put(updatedStreak);

  } catch (error) {
    console.error('Calculate user streak error:', error);
  }
}

/**
 * Calculate couple streak
 */
async function calculateCoupleStreak(coupleId, currentMonth) {
  try {
    // Get current couple streak record
    let streakRecord = await database.get(`COUPLE#${coupleId}`, `STREAK#${currentMonth}`);

    if (!streakRecord) {
      streakRecord = {
        PK: `COUPLE#${coupleId}`,
        SK: `STREAK#${currentMonth}`,
        coupleId,
        month: currentMonth,
        currentStreak: 0,
        longestStreak: 0,
        lastActivityDate: null,
        activeDays: [],
        totalActiveDays: 0,
        GSI1PK: 'TYPE#COUPLE_STREAK',
        GSI1SK: `MONTH#${currentMonth}#${coupleId}`
      };
    }

    // Get couple info
    const couple = await database.get(`COUPLE#${coupleId}`, 'METADATA');
    if (!couple) {
      return;
    }

    const { user1Id, user2Id } = couple;

    // Check activity for the last 30 days
    const today = new Date();
    const activeDays = [];
    let currentStreak = 0;
    let longestStreak = streakRecord.longestStreak || 0;

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      // Check if both users were active on this date
      const user1Active = await checkUserActivityForDate(user1Id, dateStr);
      const user2Active = await checkUserActivityForDate(user2Id, dateStr);

      if (user1Active && user2Active) {
        activeDays.push(dateStr);

        if (i === 0) { // Today or most recent day
          currentStreak = 1;

          // Count consecutive days
          for (let j = 1; j < 30; j++) {
            const prevDate = new Date(today);
            prevDate.setDate(prevDate.getDate() - j);
            const prevDateStr = prevDate.toISOString().split('T')[0];

            const prevUser1Active = await checkUserActivityForDate(user1Id, prevDateStr);
            const prevUser2Active = await checkUserActivityForDate(user2Id, prevDateStr);

            if (prevUser1Active && prevUser2Active) {
              currentStreak++;
            } else {
              break;
            }
          }
        }
      } else if (i === 0) {
        currentStreak = 0;
      }
    }

    longestStreak = Math.max(longestStreak, currentStreak);

    // Update streak record
    const updatedStreak = {
      ...streakRecord,
      currentStreak,
      longestStreak,
      lastActivityDate: activeDays.length > 0 ? activeDays[0] : streakRecord.lastActivityDate,
      activeDays: activeDays.reverse(), // Oldest first
      totalActiveDays: activeDays.length,
      updatedAt: new Date().toISOString()
    };

    await database.put(updatedStreak);

  } catch (error) {
    console.error('Calculate couple streak error:', error);
  }
}

/**
 * Check if user was active on a specific date
 */
async function checkUserActivityForDate(userId, date) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return false;
    }

    const coupleId = userProfile.coupleId;

    // Check if user watered tree on this date
    const watering = await database.get(`COUPLE#${coupleId}`, `WATERING#${date}#${userId}`);
    if (watering) {
      return true;
    }

    // Check if user answered question on this date
    const answer = await database.get(`COUPLE#${coupleId}`, `ANSWER#${date}#${userId}`);
    if (answer) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Check user activity error:', error);
    return false;
  }
}
