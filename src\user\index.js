const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');

/**
 * User Service - Handle user profile management
 */

exports.handler = async(event, context) => {
  console.log('User service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;

    switch (route) {
    case 'GET /users/profile':
      return await handleGetProfile(user);

    case 'PUT /users/profile':
      return await handleUpdateProfile(user, body);

    case 'GET /users/stats':
      return await handleGetUserStats(user);

    default:
      return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('User service error:', error);
    return response.handleError(error);
  }
};

async function handleGetProfile(user) {
  try {
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile) {
      return response.notFound('User profile not found');
    }

    const { PK, SK, GSI1PK, GSI1SK, ...profile } = userProfile;
    return response.success(profile);
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleUpdateProfile(user, body) {
  try {
    // Implementation for updating user profile
    return response.success({ message: 'Profile updated successfully' });
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleGetUserStats(user) {
  try {
    // Implementation for getting user statistics
    return response.success({ stats: {} });
  } catch (error) {
    return response.handleError(error);
  }
}
