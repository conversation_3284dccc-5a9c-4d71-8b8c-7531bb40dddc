const { validateBody } = require('../shared/validation');
const { schemas } = require('../shared/validation');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Questions Service - Handle daily questions and answers
 */

// Sample questions pool
const QUESTION_POOL = [
  {
    category: 'memories',
    questions: [
      '¿Cuál es tu recuerdo favorito de nosotros?',
      '¿Qué momento juntos te hace sonreír siempre?',
      '¿Cuál fue el día más especial que hemos compartido?',
      '¿Qué aventura juntos te gustaría repetir?',
      '¿Cuál es la conversación más profunda que hemos tenido?'
    ]
  },
  {
    category: 'dreams',
    questions: [
      '¿Cómo te imaginas nuestro futuro en 5 años?',
      '¿Qué sueño te gustaría que cumplamos juntos?',
      '¿A dónde te gustaría viajar conmigo?',
      '¿Qué tradición te gustaría crear para nosotros?',
      '¿Cómo sería nuestro hogar ideal?'
    ]
  },
  {
    category: 'feelings',
    questions: [
      '¿Qué es lo que más admiras de mí?',
      '¿Cómo me demuestras tu amor?',
      '¿Qué te hace sentir más conectado/a conmigo?',
      '¿Cuándo te sientes más orgulloso/a de nosotros?',
      '¿Qué es lo que más te gusta de nuestra relación?'
    ]
  },
  {
    category: 'growth',
    questions: [
      '¿En qué hemos crecido más como pareja?',
      '¿Qué desafío hemos superado juntos?',
      '¿Cómo me has ayudado a ser mejor persona?',
      '¿Qué hemos aprendido el uno del otro?',
      '¿En qué aspecto te gustaría que crezcamos más?'
    ]
  },
  {
    category: 'fun',
    questions: [
      '¿Cuál es la actividad más divertida que hacemos juntos?',
      '¿Qué nueva actividad te gustaría probar conmigo?',
      '¿Cuál es nuestro chiste interno favorito?',
      '¿Qué juego o hobby podríamos compartir?',
      '¿Cuál es la fecha más creativa que podríamos tener?'
    ]
  }
];

exports.handler = async(event, context) => {
  console.log('Questions service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;

    switch (route) {
    case 'GET /questions/today':
      return await handleGetTodayQuestion(user);

    case 'POST /questions/answer':
      return await handleAnswerQuestion(user, body);

    case 'GET /questions/history':
      return await handleGetQuestionHistory(user, event.queryStringParameters);

    case 'GET /questions/couple-answers':
      return await handleGetCoupleAnswers(user, pathParameters);

    case 'GET /questions/unanswered':
      return await handleGetUnansweredQuestions(user);

    case 'GET /questions/stats':
      return await handleGetQuestionStats(user);

    default:
      return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Questions service error:', error);
    return response.handleError(error);
  }
};

/**
 * Get today's question
 */
async function handleGetTodayQuestion(user) {
  try {
    const today = new Date().toISOString().split('T')[0];

    // Get today's question
    const todayQuestion = await database.get(`DAILY_QUESTION#${today}`, 'QUESTION');

    if (!todayQuestion) {
      // Create today's question if it doesn't exist
      const newQuestion = await createDailyQuestion(today);
      return response.success({
        question: newQuestion,
        hasAnswered: false,
        partnerHasAnswered: false
      });
    }

    // Check if user has answered
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.success({
        question: todayQuestion,
        hasAnswered: false,
        partnerHasAnswered: false,
        note: 'Join a couple to see partner answers'
      });
    }

    const coupleId = userProfile.coupleId;
    const partnerId = userProfile.partnerId;

    // Check user's answer
    const userAnswer = await database.get(
      `COUPLE#${coupleId}`,
      `ANSWER#${today}#${user.userId}`
    );

    // Check partner's answer
    const partnerAnswer = await database.get(
      `COUPLE#${coupleId}`,
      `ANSWER#${today}#${partnerId}`
    );

    return response.success({
      question: todayQuestion,
      hasAnswered: !!userAnswer,
      partnerHasAnswered: !!partnerAnswer,
      userAnswer: userAnswer ? userAnswer.answer : null,
      partnerAnswer: (userAnswer && partnerAnswer) ? partnerAnswer.answer : null,
      bothAnswered: !!(userAnswer && partnerAnswer)
    });
  } catch (error) {
    console.error('Get today question error:', error);
    return response.handleError(error);
  }
}

/**
 * Answer a question
 */
async function handleAnswerQuestion(user, body) {
  // Validate input
  const validation = validateBody(body, schemas.question.answer);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  const { questionId, answer } = validation.value;

  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;
    const partnerId = userProfile.partnerId;

    // Validate question exists
    const question = await database.get(`DAILY_QUESTION#${questionId}`, 'QUESTION');
    if (!question) {
      return response.notFound('Question not found');
    }

    // Check if user has already answered this question
    const existingAnswer = await database.get(
      `COUPLE#${coupleId}`,
      `ANSWER#${questionId}#${user.userId}`
    );

    if (existingAnswer) {
      return response.conflict('You have already answered this question');
    }

    // Create answer record
    const answerRecord = {
      PK: `COUPLE#${coupleId}`,
      SK: `ANSWER#${questionId}#${user.userId}`,
      answerId: uuidv4(),
      userId: user.userId,
      questionId,
      question: question.question,
      answer,
      answeredAt: new Date().toISOString(),
      GSI1PK: 'TYPE#ANSWER',
      GSI1SK: `DATE#${questionId}#${coupleId}#${user.userId}`
    };

    await database.put(answerRecord);

    // Check if partner has also answered
    const partnerAnswer = await database.get(
      `COUPLE#${coupleId}`,
      `ANSWER#${questionId}#${partnerId}`
    );

    const bothAnswered = !!partnerAnswer;

    // If both answered, create completion event
    if (bothAnswered) {
      const completionEvent = {
        PK: `EVENT#${Date.now()}`,
        SK: 'QUESTION_COMPLETED',
        eventType: 'QUESTION_COMPLETED',
        coupleId,
        questionId,
        question: question.question,
        completedAt: new Date().toISOString(),
        GSI1PK: 'TYPE#EVENT',
        GSI1SK: `QUESTION_COMPLETED#${coupleId}`,
        TTL: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
      };

      await database.put(completionEvent);
    }

    return response.created({
      answerId: answerRecord.answerId,
      message: 'Answer saved successfully',
      bothAnswered,
      partnerAnswer: bothAnswered ? partnerAnswer.answer : null,
      nextQuestion: bothAnswered ? 'Both answered! Come back tomorrow for a new question.' : 'Waiting for your partner to answer.'
    });
  } catch (error) {
    console.error('Answer question error:', error);
    return response.handleError(error);
  }
}

/**
 * Get question history
 */
async function handleGetQuestionHistory(user, queryParams) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;
    const limit = parseInt(queryParams?.limit) || 20;

    // Get answered questions for this couple
    const answers = await database.query(
      `COUPLE#${coupleId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'ANSWER#' }
      },
      {
        ScanIndexForward: false, // Most recent first
        Limit: limit * 2 // Get more to account for both users' answers
      }
    );

    // Group answers by question date
    const questionMap = new Map();

    answers.items.forEach(answer => {
      const questionId = answer.questionId;
      if (!questionMap.has(questionId)) {
        questionMap.set(questionId, {
          questionId,
          question: answer.question,
          date: questionId,
          answers: {}
        });
      }

      questionMap.get(questionId).answers[answer.userId] = {
        userId: answer.userId,
        answer: answer.answer,
        answeredAt: answer.answeredAt
      };
    });

    // Convert to array and sort by date
    const questionHistory = Array.from(questionMap.values())
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, limit);

    return response.success({
      coupleId,
      history: questionHistory,
      total: questionHistory.length
    });
  } catch (error) {
    console.error('Get question history error:', error);
    return response.handleError(error);
  }
}

/**
 * Get couple answers for a specific date
 */
async function handleGetCoupleAnswers(user, pathParameters) {
  const { date } = pathParameters;

  if (!date) {
    return response.validationError([
      { field: 'date', message: 'Date parameter is required' }
    ]);
  }

  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;
    const partnerId = userProfile.partnerId;

    // Get question for the date
    const question = await database.get(`DAILY_QUESTION#${date}`, 'QUESTION');
    if (!question) {
      return response.notFound('No question found for this date');
    }

    // Get both answers
    const userAnswer = await database.get(
      `COUPLE#${coupleId}`,
      `ANSWER#${date}#${user.userId}`
    );

    const partnerAnswer = await database.get(
      `COUPLE#${coupleId}`,
      `ANSWER#${date}#${partnerId}`
    );

    return response.success({
      date,
      question: question.question,
      category: question.category,
      answers: {
        user: userAnswer ? {
          answer: userAnswer.answer,
          answeredAt: userAnswer.answeredAt
        } : null,
        partner: partnerAnswer ? {
          answer: partnerAnswer.answer,
          answeredAt: partnerAnswer.answeredAt
        } : null
      },
      bothAnswered: !!(userAnswer && partnerAnswer)
    });
  } catch (error) {
    console.error('Get couple answers error:', error);
    return response.handleError(error);
  }
}

/**
 * Get unanswered questions
 */
async function handleGetUnansweredQuestions(user) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;

    // Get recent questions (last 7 days)
    const recentQuestions = [];
    const today = new Date();

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const question = await database.get(`DAILY_QUESTION#${dateStr}`, 'QUESTION');
      if (question) {
        recentQuestions.push({ ...question, date: dateStr });
      }
    }

    // Check which ones user hasn't answered
    const unanswered = [];

    for (const question of recentQuestions) {
      const userAnswer = await database.get(
        `COUPLE#${coupleId}`,
        `ANSWER#${question.date}#${user.userId}`
      );

      if (!userAnswer) {
        unanswered.push(question);
      }
    }

    return response.success({
      unanswered,
      total: unanswered.length
    });
  } catch (error) {
    console.error('Get unanswered questions error:', error);
    return response.handleError(error);
  }
}

/**
 * Get question statistics
 */
async function handleGetQuestionStats(user) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;
    const partnerId = userProfile.partnerId;

    // Get all answers for this couple
    const allAnswers = await database.query(
      `COUPLE#${coupleId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'ANSWER#' }
      }
    );

    // Calculate statistics
    const userAnswers = allAnswers.items.filter(a => a.userId === user.userId);
    const partnerAnswers = allAnswers.items.filter(a => a.userId === partnerId);

    // Group by date to find completed questions
    const questionDates = new Set();
    allAnswers.items.forEach(answer => {
      questionDates.add(answer.questionId);
    });

    let completedQuestions = 0;
    for (const date of questionDates) {
      const userAnswered = userAnswers.some(a => a.questionId === date);
      const partnerAnswered = partnerAnswers.some(a => a.questionId === date);
      if (userAnswered && partnerAnswered) {
        completedQuestions++;
      }
    }

    // Calculate streak (consecutive days both answered)
    let currentStreak = 0;
    const today = new Date();

    for (let i = 0; i < 30; i++) { // Check last 30 days
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const userAnswered = userAnswers.some(a => a.questionId === dateStr);
      const partnerAnswered = partnerAnswers.some(a => a.questionId === dateStr);

      if (userAnswered && partnerAnswered) {
        currentStreak++;
      } else {
        break;
      }
    }

    return response.success({
      stats: {
        totalQuestionsAnswered: userAnswers.length,
        partnerQuestionsAnswered: partnerAnswers.length,
        completedTogether: completedQuestions,
        currentStreak,
        totalQuestions: questionDates.size,
        completionRate: questionDates.size > 0 ? Math.round((completedQuestions / questionDates.size) * 100) : 0
      }
    });
  } catch (error) {
    console.error('Get question stats error:', error);
    return response.handleError(error);
  }
}

/**
 * Create a daily question (used by scheduler)
 */
async function createDailyQuestion(date) {
  try {
    // Select random category and question
    const randomCategory = QUESTION_POOL[Math.floor(Math.random() * QUESTION_POOL.length)];
    const randomQuestion = randomCategory.questions[Math.floor(Math.random() * randomCategory.questions.length)];

    const questionRecord = {
      PK: `DAILY_QUESTION#${date}`,
      SK: 'QUESTION',
      date,
      question: randomQuestion,
      category: randomCategory.category,
      createdAt: new Date().toISOString(),
      GSI1PK: 'TYPE#DAILY_QUESTION',
      GSI1SK: `DATE#${date}`
    };

    await database.put(questionRecord);
    return questionRecord;
  } catch (error) {
    console.error('Create daily question error:', error);
    throw error;
  }
}

module.exports = { createDailyQuestion };
