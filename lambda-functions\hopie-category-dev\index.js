const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/categories')) {
            return await getCategories(event);
        } else if (httpMethod === 'POST' && path.includes('/categories')) {
            return await createCategory(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/category/')) {
            return await getCategoryById(event);
        } else if (httpMethod === 'PUT' && path.includes('/category/')) {
            return await updateCategory(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/category/')) {
            return await deleteCategory(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getCategories(event) {
    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'CATEGORY'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            categories: result.Items || []
        })
    };
}

async function createCategory(body, event) {
    const categoryId = `category-${Date.now()}`;
    const { name, description, color } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA',
            GSI1PK: 'CATEGORY',
            GSI1SK: categoryId,
            id: categoryId,
            name,
            description,
            color,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Category created successfully',
            category: params.Item
        })
    };
}

async function getCategoryById(event) {
    const categoryId = event.pathParameters?.id || 'category-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            category: result.Item || null
        })
    };
}

async function updateCategory(body, event) {
    const categoryId = event.pathParameters?.id || 'category-123';
    const { name, description, color } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #name = :name, description = :description, color = :color, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':description': description,
            ':color': color,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Category updated successfully',
            category: result.Attributes
        })
    };
}

async function deleteCategory(event) {
    const categoryId = event.pathParameters?.id || 'category-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `CATEGORY#${categoryId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Category deleted successfully'
        })
    };
}
