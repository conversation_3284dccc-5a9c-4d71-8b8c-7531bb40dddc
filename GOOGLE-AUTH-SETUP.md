# 🔐 Configuración de Autenticación con Google para HopieApp

## 📋 Pasos para Configurar Google OAuth

### 1. **Crear Proyecto en Google Cloud Console**

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la **Google+ API** y **Google Identity API**

### 2. **Configurar OAuth 2.0**

1. Ve a **APIs & Services** > **Credentials**
2. Haz clic en **+ CREATE CREDENTIALS** > **OAuth 2.0 Client IDs**
3. Selecciona **Web application**
4. Configura las URLs:

#### **URLs de Redirección Autorizadas:**
```
https://hopie-app-dev.auth.us-east-2.amazoncognito.com/oauth2/idpresponse
https://hopie-app-staging.auth.us-east-2.amazoncognito.com/oauth2/idpresponse
https://hopie-app-prod.auth.us-east-2.amazoncognito.com/oauth2/idpresponse
http://localhost:3000/auth/callback
```

#### **Orígenes JavaScript Autorizados:**
```
https://hopie-app-dev.auth.us-east-2.amazoncognito.com
https://hopie-app-staging.auth.us-east-2.amazoncognito.com
https://hopie-app-prod.auth.us-east-2.amazoncognito.com
http://localhost:3000
```

### 3. **Obtener Credenciales**

Después de crear el cliente OAuth, obtendrás:
- **Client ID**: `123456789-abcdef.apps.googleusercontent.com`
- **Client Secret**: `GOCSPX-abcdef123456`

## 🚀 Desplegar con Google Auth

### **Opción 1: Con Google Auth**
```powershell
.\deploy-single-stack.ps1 -Stage dev -GoogleClientId "TU_GOOGLE_CLIENT_ID" -GoogleClientSecret "TU_GOOGLE_CLIENT_SECRET"
```

### **Opción 2: Solo Cognito (sin Google)**
```powershell
.\deploy-single-stack.ps1 -Stage dev
```

### **Opción 3: Despliegue por fases**
```powershell
# Primero sin Lambdas
.\deploy-single-stack.ps1 -Stage dev -SkipLambdas -GoogleClientId "TU_ID" -GoogleClientSecret "TU_SECRET"

# Luego agregar Lambdas
.\deploy-single-stack.ps1 -Stage dev -GoogleClientId "TU_ID" -GoogleClientSecret "TU_SECRET"
```

## 🔧 Configuración en tu Aplicación Frontend

### **URLs que necesitarás:**

Después del despliegue, obtendrás estas URLs en los outputs:

```javascript
const cognitoConfig = {
  region: 'us-east-2',
  userPoolId: 'us-east-2_XXXXXXXXX',
  userPoolWebClientId: 'abcdef123456',
  domain: 'hopie-app-dev.auth.us-east-2.amazoncognito.com',
  redirectSignIn: 'http://localhost:3000/auth/callback',
  redirectSignOut: 'http://localhost:3000/auth/logout',
  responseType: 'code'
};
```

### **Ejemplo de implementación con Amplify:**

```javascript
import { Amplify } from 'aws-amplify';

Amplify.configure({
  Auth: {
    region: cognitoConfig.region,
    userPoolId: cognitoConfig.userPoolId,
    userPoolWebClientId: cognitoConfig.userPoolWebClientId,
    oauth: {
      domain: cognitoConfig.domain,
      scope: ['email', 'openid', 'profile'],
      redirectSignIn: cognitoConfig.redirectSignIn,
      redirectSignOut: cognitoConfig.redirectSignOut,
      responseType: cognitoConfig.responseType
    }
  }
});
```

### **Botones de autenticación:**

```javascript
import { Auth } from 'aws-amplify';

// Iniciar sesión con Google
const signInWithGoogle = () => {
  Auth.federatedSignIn({ provider: 'Google' });
};

// Iniciar sesión con Cognito
const signInWithCognito = (username, password) => {
  Auth.signIn(username, password);
};

// Cerrar sesión
const signOut = () => {
  Auth.signOut();
};
```

## 🔍 Verificar Configuración

### **1. Probar en Cognito Console:**
1. Ve a AWS Cognito Console
2. Selecciona tu User Pool
3. Ve a **App integration** > **Domain**
4. Haz clic en **Launch Hosted UI**
5. Deberías ver el botón "Continue with Google"

### **2. Verificar Identity Provider:**
1. En Cognito Console, ve a **Sign-in experience**
2. Ve a **Federated identity provider sign-in**
3. Deberías ver "Google" listado

## 🚨 Solución de Problemas

### **Error: "Invalid redirect URI"**
- Verifica que las URLs en Google Cloud Console coincidan exactamente
- Asegúrate de incluir `https://` y no `http://` para producción

### **Error: "User pool client does not support Google"**
- Verifica que el GoogleClientId y GoogleClientSecret sean correctos
- Asegúrate de que el despliegue se completó exitosamente

### **Error: "Access denied"**
- Verifica que las APIs estén habilitadas en Google Cloud Console
- Revisa que el proyecto de Google Cloud esté activo

## 📝 Notas Importantes

1. **Desarrollo Local**: Usa `http://localhost:3000` para desarrollo
2. **Producción**: Siempre usa `https://` para URLs de producción
3. **Dominios**: Los dominios de Cognito se generan automáticamente
4. **Scopes**: Los scopes `email`, `openid`, `profile` están preconfigurados
5. **Atributos**: Se mapean automáticamente: email, name, given_name, family_name, picture

## 🔄 Actualizar Configuración

Para cambiar las credenciales de Google después del despliegue:

```powershell
.\deploy-single-stack.ps1 -Stage dev -GoogleClientId "NUEVO_CLIENT_ID" -GoogleClientSecret "NUEVO_CLIENT_SECRET"
```

El stack se actualizará sin recrear otros recursos gracias a las políticas de retención.
