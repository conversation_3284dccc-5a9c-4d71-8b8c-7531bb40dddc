AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Parameters:
  Stage:
    Type: String
    Default: dev
    Description: Deployment stage

Resources:
  # ===== API GATEWAY =====
  HopieApi:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub 'hopie-api-${Stage}'
      StageName: !Ref Stage
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      Auth:
        DefaultAuthorizer: CognitoAuthorizer
        Authorizers:
          CognitoAuthorizer:
            UserPoolArn: !Sub 
              - 'arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${UserPoolId}'
              - UserPoolId: !ImportValue 'hopie-app-dev-CognitoUserPoolId'
        AddDefaultAuthorizerToCorsPreflight: false

  # ===== API GATEWAY METHODS =====
  # Auth endpoints
  AuthApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: auth

  AuthProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref AuthApiResource
      PathPart: '{proxy+}'

  AuthMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref AuthProxyResource
      HttpMethod: ANY
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-auth-${Stage}'

  # Users endpoints
  UsersApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: users

  UsersProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref UsersApiResource
      PathPart: '{proxy+}'

  UsersMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref UsersProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-user-${Stage}'

  # Couples endpoints
  CouplesApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: couples

  CouplesProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref CouplesApiResource
      PathPart: '{proxy+}'

  CouplesMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref CouplesProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-couple-${Stage}'

  # Trees endpoints
  TreesApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: trees

  TreesProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref TreesApiResource
      PathPart: '{proxy+}'

  TreesMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref TreesProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-tree-${Stage}'

  # Questions endpoints
  QuestionsApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: questions

  QuestionsProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref QuestionsApiResource
      PathPart: '{proxy+}'

  QuestionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref QuestionsProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-questions-${Stage}'

  # LifePlan endpoints
  LifePlanApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: lifeplan

  LifePlanProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref LifePlanApiResource
      PathPart: '{proxy+}'

  LifePlanMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref LifePlanProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-lifeplan-${Stage}'

  # Places endpoints
  PlacesApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: places

  PlacesProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref PlacesApiResource
      PathPart: '{proxy+}'

  PlacesMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref PlacesProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-places-${Stage}'

  # Location endpoints
  LocationApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: location

  LocationProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref LocationApiResource
      PathPart: '{proxy+}'

  LocationMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref LocationProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-location-${Stage}'

  # Stats endpoints
  StatsApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: stats

  StatsProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref StatsApiResource
      PathPart: '{proxy+}'

  StatsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref StatsProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-stats-${Stage}'

  # Images endpoints
  ImagesApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: images

  ImagesProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref ImagesApiResource
      PathPart: '{proxy+}'

  ImagesMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref ImagesProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-image-${Stage}'

  # Notifications endpoints
  NotificationsApiResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !GetAtt HopieApi.RootResourceId
      PathPart: notifications

  NotificationsProxyResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref HopieApi
      ParentId: !Ref NotificationsApiResource
      PathPart: '{proxy+}'

  NotificationsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref HopieApi
      ResourceId: !Ref NotificationsProxyResource
      HttpMethod: ANY
      AuthorizationType: COGNITO_USER_POOLS
      AuthorizerId: !Ref CognitoAuthorizer
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:hopie-notification-${Stage}'

  # ===== COGNITO AUTHORIZER =====
  CognitoAuthorizer:
    Type: AWS::ApiGateway::Authorizer
    Properties:
      Name: CognitoAuthorizer
      Type: COGNITO_USER_POOLS
      IdentitySource: method.request.header.Authorization
      RestApiId: !Ref HopieApi
      ProviderARNs:
        - !Sub
          - 'arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${UserPoolId}'
          - UserPoolId: !ImportValue 'hopie-app-dev-CognitoUserPoolId'

  # ===== LAMBDA PERMISSIONS =====
  AuthLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-auth-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  UserLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-user-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  CoupleLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-couple-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  TreeLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-tree-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  QuestionsLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-questions-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  LifePlanLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-lifeplan-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  PlacesLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-places-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  LocationLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-location-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  StatsLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-stats-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  ImageLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-image-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  NotificationLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub 'hopie-notification-${Stage}'
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HopieApi}/*/*'

  # ===== API DEPLOYMENT =====
  ApiDeployment:
    Type: AWS::ApiGateway::Deployment
    DependsOn:
      - AuthMethod
      - UsersMethod
      - CouplesMethod
      - TreesMethod
      - QuestionsMethod
      - LifePlanMethod
      - PlacesMethod
      - LocationMethod
      - StatsMethod
      - ImagesMethod
      - NotificationsMethod
    Properties:
      RestApiId: !Ref HopieApi
      StageName: !Ref Stage

Outputs:
  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !Sub 'https://${HopieApi}.execute-api.${AWS::Region}.amazonaws.com/${Stage}'
    Export:
      Name: !Sub '${AWS::StackName}-ApiUrl'

  ApiGatewayId:
    Description: API Gateway ID
    Value: !Ref HopieApi
    Export:
      Name: !Sub '${AWS::StackName}-ApiId'
