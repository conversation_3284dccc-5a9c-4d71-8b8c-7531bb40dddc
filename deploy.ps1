param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipLambdas,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipWebSocket,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [switch]$DisableRollback,
    
    [Parameter(Mandatory=$false)]
    [string]$StackName = "HopieApp-$Stage"
)

# Colores para output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput $Blue "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "❌ $message"
}

# Cargar configuración
Write-Info "Cargando configuración para el stage: $Stage"
$configPath = "deploy-config.json"
if (-not (Test-Path $configPath)) {
    Write-Error "Archivo de configuración no encontrado: $configPath"
    exit 1
}

$config = Get-Content $configPath | ConvertFrom-Json
$stageConfig = $config.$Stage

if (-not $stageConfig) {
    Write-Error "Configuración no encontrada para el stage: $Stage"
    exit 1
}

# Preparar parámetros
$parameters = @()
$parameters += "Stage=$Stage"
$parameters += "CognitoDomainPrefix=$($stageConfig.cognitoDomainPrefix)"

if ($SkipLambdas) {
    $parameters += "EnableLambdaFunctions=false"
    Write-Warning "Las funciones Lambda serán omitidas"
} else {
    $parameters += "EnableLambdaFunctions=$($stageConfig.enableLambdaFunctions)"
}

if ($SkipWebSocket) {
    $parameters += "EnableWebSocket=false"
    Write-Warning "WebSocket API será omitido"
} else {
    $parameters += "EnableWebSocket=$($stageConfig.enableWebSocket)"
}

# Construir comando SAM
$samCommand = @(
    "sam", "deploy"
    "--template-file", "template.yaml"
    "--stack-name", $StackName
    "--capabilities", "CAPABILITY_IAM", "CAPABILITY_AUTO_EXPAND"
    "--parameter-overrides"
) + $parameters

if ($DisableRollback) {
    $samCommand += "--disable-rollback"
    Write-Warning "Rollback automático deshabilitado"
}

if ($DryRun) {
    $samCommand += "--no-execute-changeset"
    Write-Info "Modo DRY RUN - No se ejecutarán cambios"
}

# Verificar que SAM CLI esté instalado
Write-Info "Verificando SAM CLI..."
try {
    $samVersion = sam --version
    Write-Success "SAM CLI encontrado: $samVersion"
} catch {
    Write-Error "SAM CLI no está instalado o no está en el PATH"
    Write-Info "Instala SAM CLI desde: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
    exit 1
}

# Verificar credenciales AWS
Write-Info "Verificando credenciales AWS..."
try {
    $awsIdentity = aws sts get-caller-identity --output json | ConvertFrom-Json
    Write-Success "Credenciales AWS válidas - Account: $($awsIdentity.Account), User: $($awsIdentity.Arn)"
} catch {
    Write-Error "Credenciales AWS no válidas o AWS CLI no configurado"
    exit 1
}

# Construir aplicación
Write-Info "Construyendo aplicación SAM..."
try {
    sam build
    if ($LASTEXITCODE -ne 0) {
        throw "Error en sam build"
    }
    Write-Success "Aplicación construida exitosamente"
} catch {
    Write-Error "Error al construir la aplicación: $_"
    exit 1
}

# Mostrar comando que se ejecutará
Write-Info "Comando a ejecutar:"
Write-ColorOutput $Blue ($samCommand -join " ")

# Confirmar despliegue
if (-not $DryRun) {
    $confirmation = Read-Host "¿Continuar con el despliegue? (y/N)"
    if ($confirmation -ne "y" -and $confirmation -ne "Y") {
        Write-Warning "Despliegue cancelado por el usuario"
        exit 0
    }
}

# Ejecutar despliegue
Write-Info "Iniciando despliegue..."
try {
    & $samCommand[0] $samCommand[1..($samCommand.Length-1)]
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "¡Despliegue completado exitosamente!"
        
        # Mostrar outputs del stack
        Write-Info "Obteniendo outputs del stack..."
        $outputs = aws cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs" --output table
        Write-Output $outputs
        
    } else {
        Write-Error "El despliegue falló con código de salida: $LASTEXITCODE"
        
        # Mostrar eventos del stack para debugging
        Write-Info "Últimos eventos del stack:"
        aws cloudformation describe-stack-events --stack-name $StackName --max-items 10 --output table
        
        exit $LASTEXITCODE
    }
} catch {
    Write-Error "Error durante el despliegue: $_"
    exit 1
}
