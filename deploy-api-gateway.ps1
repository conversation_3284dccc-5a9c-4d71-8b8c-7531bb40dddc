# Script para desplegar solo el API Gateway y conectarlo con todas las Lambdas
Write-Host "Desplegando API Gateway para HopieApp..." -ForegroundColor Green

# Obtener el ID del Cognito User Pool del stack existente
Write-Host "Obteniendo informacion del stack existente..." -ForegroundColor Yellow
$cognitoUserPoolId = aws cloudformation describe-stack-resources --stack-name hopie-app-dev --query "StackResources[?ResourceType=='AWS::Cognito::UserPool'].PhysicalResourceId" --output text

if (-not $cognitoUserPoolId) {
    Write-Host "Error: No se encontro el Cognito User Pool en el stack." -ForegroundColor Red
    exit 1
}

Write-Host "Cognito User Pool ID encontrado: $cognitoUserPoolId" -ForegroundColor Green

# Crear el template con el User Pool ID correcto
Write-Host "Creando template con configuracion correcta..." -ForegroundColor Yellow

# Reemplazar el ImportValue con el ID real
(Get-Content fix-api-gateway.yaml) -replace "!ImportValue 'hopie-app-dev-CognitoUserPoolId'", "'$cognitoUserPoolId'" | Set-Content fix-api-gateway-final.yaml

# Desplegar el API Gateway en el mismo stack
Write-Host "Desplegando API Gateway..." -ForegroundColor Green

aws cloudformation deploy --template-file fix-api-gateway-final.yaml --stack-name hopie-app-dev-api-gateway --parameter-overrides Stage=dev --capabilities CAPABILITY_IAM --no-fail-on-empty-changeset

if ($LASTEXITCODE -eq 0) {
    Write-Host "API Gateway desplegado exitosamente!" -ForegroundColor Green

    # Obtener la URL del API Gateway
    $apiUrl = aws cloudformation describe-stacks --stack-name hopie-app-dev-api-gateway --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text

    Write-Host "URL del API Gateway: $apiUrl" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Endpoints disponibles:" -ForegroundColor Yellow
    Write-Host "  POST $apiUrl/auth/login" -ForegroundColor White
    Write-Host "  POST $apiUrl/auth/register" -ForegroundColor White
    Write-Host "  GET  $apiUrl/users/profile" -ForegroundColor White
    Write-Host "  POST $apiUrl/couples/create" -ForegroundColor White
    Write-Host "  GET  $apiUrl/trees/family" -ForegroundColor White
    Write-Host "  GET  $apiUrl/questions/daily" -ForegroundColor White
    Write-Host "  POST $apiUrl/lifeplan/goals" -ForegroundColor White
    Write-Host "  GET  $apiUrl/places/favorites" -ForegroundColor White
    Write-Host "  POST $apiUrl/location/update" -ForegroundColor White
    Write-Host "  GET  $apiUrl/stats/dashboard" -ForegroundColor White
    Write-Host "  POST $apiUrl/images/upload" -ForegroundColor White
    Write-Host "  GET  $apiUrl/notifications/list" -ForegroundColor White
    Write-Host ""
    Write-Host "Tu aplicacion HopieApp esta lista para consumir!" -ForegroundColor Green

} else {
    Write-Host "Error al desplegar el API Gateway" -ForegroundColor Red
    exit 1
}

# Limpiar archivo temporal
Remove-Item fix-api-gateway-final.yaml -ErrorAction SilentlyContinue

Write-Host "Proceso completado!" -ForegroundColor Green
