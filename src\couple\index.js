const { validateBody } = require('../shared/validation');
const { schemas } = require('../shared/validation');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Couple Service - Handle couple formation, invitations, and management
 */

exports.handler = async(event, context) => {
  console.log('Couple service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;

    switch (route) {
    case 'POST /couples/invite':
      return await handleInvitePartner(user, body);

    case 'POST /couples/accept':
      return await handleAcceptInvitation(user, body);

    case 'POST /couples/reject':
      return await handleRejectInvitation(user, body);

    case 'GET /couples/info':
      return await handleGetCoupleInfo(user);

    case 'PUT /couples/settings':
      return await handleUpdateCoupleSettings(user, body);

    case 'DELETE /couples/disconnect':
      return await handleDisconnectCouple(user);

    case 'GET /couples/invitations':
      return await handleGetInvitations(user);

    default:
      return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Couple service error:', error);
    return response.handleError(error);
  }
};

/**
 * Send invitation to partner
 */
async function handleInvitePartner(user, body) {
  // Validate input
  const validation = validateBody(body, schemas.couple.invite);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  const { email } = validation.value;

  try {
    // Check if user is already in a couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (userProfile && userProfile.coupleId) {
      return response.conflict('You are already part of a couple');
    }

    // Check if inviting themselves
    if (email.toLowerCase() === user.email.toLowerCase()) {
      return response.validationError([
        { field: 'email', message: 'You cannot invite yourself' }
      ]);
    }

    // Find the partner by email
    const users = await database.queryGSI1('TYPE#USER');
    const partner = users.items.find(u => u.email.toLowerCase() === email.toLowerCase());

    if (!partner) {
      return response.notFound('User with this email not found');
    }

    // Check if partner is already in a couple
    if (partner.coupleId) {
      return response.conflict('This user is already part of a couple');
    }

    // Check if there's already a pending invitation
    const existingInvitation = await database.get(
      `INVITATION#${user.userId}#${partner.userId}`,
      'METADATA'
    );

    if (existingInvitation) {
      return response.conflict('Invitation already sent to this user');
    }

    // Check for reverse invitation
    const reverseInvitation = await database.get(
      `INVITATION#${partner.userId}#${user.userId}`,
      'METADATA'
    );

    if (reverseInvitation) {
      return response.conflict('This user has already invited you. Please check your invitations.');
    }

    // Create invitation
    const invitationId = uuidv4();
    const invitation = {
      PK: `INVITATION#${user.userId}#${partner.userId}`,
      SK: 'METADATA',
      invitationId,
      fromUserId: user.userId,
      fromUserName: user.name,
      fromUserEmail: user.email,
      toUserId: partner.userId,
      toUserName: partner.name,
      toUserEmail: partner.email,
      status: 'pending',
      sentAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      GSI1PK: 'TYPE#INVITATION',
      GSI1SK: `TO#${partner.userId}#${new Date().toISOString()}`
    };

    await database.put(invitation);

    // Create notification event
    const notificationEvent = {
      PK: `EVENT#${Date.now()}`,
      SK: 'COUPLE_INVITATION',
      eventType: 'COUPLE_INVITATION',
      fromUserId: user.userId,
      toUserId: partner.userId,
      invitationId,
      timestamp: new Date().toISOString(),
      GSI1PK: 'TYPE#EVENT',
      GSI1SK: `COUPLE_INVITATION#${partner.userId}`,
      TTL: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
    };

    await database.put(notificationEvent);

    return response.created({
      invitationId,
      message: `Invitation sent to ${partner.name} (${partner.email})`,
      partner: {
        userId: partner.userId,
        name: partner.name,
        email: partner.email
      },
      expiresAt: invitation.expiresAt
    });
  } catch (error) {
    console.error('Invite partner error:', error);
    return response.handleError(error);
  }
}

/**
 * Accept invitation to form a couple
 */
async function handleAcceptInvitation(user, body) {
  const { invitationId } = body;

  if (!invitationId) {
    return response.validationError([
      { field: 'invitationId', message: 'Invitation ID is required' }
    ]);
  }

  try {
    // Check if user is already in a couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (userProfile && userProfile.coupleId) {
      return response.conflict('You are already part of a couple');
    }

    // Find the invitation
    const invitations = await database.queryGSI1(
      'TYPE#INVITATION',
      {
        expression: 'begins_with(GSI1SK, :sk)',
        values: { ':sk': `TO#${user.userId}` }
      }
    );

    const invitation = invitations.items.find(inv => inv.invitationId === invitationId);

    if (!invitation) {
      return response.notFound('Invitation not found');
    }

    if (invitation.status !== 'pending') {
      return response.conflict('Invitation is no longer pending');
    }

    // Check if invitation has expired
    if (new Date(invitation.expiresAt) < new Date()) {
      return response.conflict('Invitation has expired');
    }

    // Create couple
    const coupleId = uuidv4();
    const couple = {
      PK: `COUPLE#${coupleId}`,
      SK: 'METADATA',
      coupleId,
      user1Id: invitation.fromUserId,
      user2Id: invitation.toUserId,
      level: 0,
      experiencePoints: 0,
      treeType: null,
      currentStage: null,
      status: 'active',
      formedAt: new Date().toISOString(),
      GSI1PK: 'TYPE#COUPLE',
      GSI1SK: `CREATED#${new Date().toISOString()}`
    };

    // Update both users with couple ID
    const transactItems = [
      // Create couple
      {
        Put: {
          TableName: process.env.DYNAMODB_TABLE,
          Item: couple
        }
      },
      // Update user 1
      {
        Update: {
          TableName: process.env.DYNAMODB_TABLE,
          Key: {
            PK: `USER#${invitation.fromUserId}`,
            SK: 'PROFILE'
          },
          UpdateExpression: 'SET coupleId = :coupleId, partnerId = :partnerId, coupleFormedAt = :formedAt',
          ExpressionAttributeValues: {
            ':coupleId': coupleId,
            ':partnerId': invitation.toUserId,
            ':formedAt': new Date().toISOString()
          }
        }
      },
      // Update user 2
      {
        Update: {
          TableName: process.env.DYNAMODB_TABLE,
          Key: {
            PK: `USER#${invitation.toUserId}`,
            SK: 'PROFILE'
          },
          UpdateExpression: 'SET coupleId = :coupleId, partnerId = :partnerId, coupleFormedAt = :formedAt',
          ExpressionAttributeValues: {
            ':coupleId': coupleId,
            ':partnerId': invitation.fromUserId,
            ':formedAt': new Date().toISOString()
          }
        }
      },
      // Update invitation status
      {
        Update: {
          TableName: process.env.DYNAMODB_TABLE,
          Key: {
            PK: invitation.PK,
            SK: invitation.SK
          },
          UpdateExpression: 'SET #status = :status, acceptedAt = :acceptedAt',
          ExpressionAttributeValues: {
            ':status': 'accepted',
            ':acceptedAt': new Date().toISOString()
          },
          ExpressionAttributeNames: {
            '#status': 'status'
          }
        }
      }
    ];

    await database.transactWrite(transactItems);

    // Create couple formation event
    const coupleEvent = {
      PK: `EVENT#${Date.now()}`,
      SK: 'COUPLE_FORMED',
      eventType: 'COUPLE_FORMED',
      coupleId,
      user1Id: invitation.fromUserId,
      user2Id: invitation.toUserId,
      timestamp: new Date().toISOString(),
      GSI1PK: 'TYPE#EVENT',
      GSI1SK: `COUPLE_FORMED#${coupleId}`,
      TTL: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
    };

    await database.put(coupleEvent);

    return response.created({
      coupleId,
      message: 'Couple formed successfully!',
      partner: {
        userId: invitation.fromUserId,
        name: invitation.fromUserName,
        email: invitation.fromUserEmail
      },
      formedAt: couple.formedAt,
      nextStep: 'Select a tree type to start your journey together'
    });
  } catch (error) {
    console.error('Accept invitation error:', error);
    return response.handleError(error);
  }
}

/**
 * Reject invitation
 */
async function handleRejectInvitation(user, body) {
  const { invitationId } = body;

  if (!invitationId) {
    return response.validationError([
      { field: 'invitationId', message: 'Invitation ID is required' }
    ]);
  }

  try {
    // Find the invitation
    const invitations = await database.queryGSI1(
      'TYPE#INVITATION',
      {
        expression: 'begins_with(GSI1SK, :sk)',
        values: { ':sk': `TO#${user.userId}` }
      }
    );

    const invitation = invitations.items.find(inv => inv.invitationId === invitationId);

    if (!invitation) {
      return response.notFound('Invitation not found');
    }

    if (invitation.status !== 'pending') {
      return response.conflict('Invitation is no longer pending');
    }

    // Update invitation status
    await database.update(
      invitation.PK,
      invitation.SK,
      'SET #status = :status, rejectedAt = :rejectedAt',
      {
        ':status': 'rejected',
        ':rejectedAt': new Date().toISOString()
      },
      {
        '#status': 'status'
      }
    );

    return response.success({
      message: 'Invitation rejected successfully'
    });
  } catch (error) {
    console.error('Reject invitation error:', error);
    return response.handleError(error);
  }
}

/**
 * Get couple information
 */
async function handleGetCoupleInfo(user) {
  try {
    // Get user profile
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;

    // Get couple information
    const couple = await database.get(`COUPLE#${coupleId}`, 'METADATA');
    if (!couple) {
      return response.notFound('Couple not found');
    }

    // Get partner information
    const partnerId = userProfile.partnerId;
    const partner = await database.get(`USER#${partnerId}`, 'PROFILE');

    // Get tree progress if exists
    const treeProgress = await database.get(`COUPLE#${coupleId}`, 'TREE#PROGRESS');

    return response.success({
      couple: {
        coupleId: couple.coupleId,
        level: couple.level,
        experiencePoints: couple.experiencePoints,
        treeType: couple.treeType,
        currentStage: couple.currentStage,
        status: couple.status,
        formedAt: couple.formedAt
      },
      partner: partner ? {
        userId: partner.userId,
        name: partner.name,
        email: partner.email,
        avatarUrl: partner.avatarUrl
      } : null,
      treeProgress: treeProgress ? {
        level: treeProgress.level,
        currentStage: treeProgress.currentStage,
        experiencePoints: treeProgress.experiencePoints,
        totalWaterings: treeProgress.totalWaterings,
        lastWatered: treeProgress.lastWatered
      } : null
    });
  } catch (error) {
    console.error('Get couple info error:', error);
    return response.handleError(error);
  }
}

/**
 * Update couple settings
 */
async function handleUpdateCoupleSettings(user, body) {
  // Validate input
  const validation = validateBody(body, schemas.couple.updateSettings);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  try {
    // Get user profile
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;
    const updates = validation.value;

    // Build update expression
    const updateExpressions = [];
    const expressionAttributeValues = {};

    if (updates.notifications !== undefined) {
      updateExpressions.push('notifications = :notifications');
      expressionAttributeValues[':notifications'] = updates.notifications;
    }

    if (updates.privacy) {
      updateExpressions.push('privacy = :privacy');
      expressionAttributeValues[':privacy'] = updates.privacy;
    }

    if (updateExpressions.length === 0) {
      return response.validationError([
        { message: 'No valid fields to update' }
      ]);
    }

    const updateExpression = 'SET ' + updateExpressions.join(', ');

    const updatedCouple = await database.update(
      `COUPLE#${coupleId}`,
      'METADATA',
      updateExpression,
      expressionAttributeValues
    );

    return response.success({
      coupleId,
      settings: {
        notifications: updatedCouple.notifications,
        privacy: updatedCouple.privacy
      }
    });
  } catch (error) {
    console.error('Update couple settings error:', error);
    return response.handleError(error);
  }
}

/**
 * Disconnect couple (break up)
 */
async function handleDisconnectCouple(user) {
  try {
    // Get user profile
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;
    const partnerId = userProfile.partnerId;

    // Update couple status to disconnected
    const transactItems = [
      // Update couple status
      {
        Update: {
          TableName: process.env.DYNAMODB_TABLE,
          Key: {
            PK: `COUPLE#${coupleId}`,
            SK: 'METADATA'
          },
          UpdateExpression: 'SET #status = :status, disconnectedAt = :disconnectedAt, disconnectedBy = :disconnectedBy',
          ExpressionAttributeValues: {
            ':status': 'disconnected',
            ':disconnectedAt': new Date().toISOString(),
            ':disconnectedBy': user.userId
          },
          ExpressionAttributeNames: {
            '#status': 'status'
          }
        }
      },
      // Remove couple info from user 1
      {
        Update: {
          TableName: process.env.DYNAMODB_TABLE,
          Key: {
            PK: `USER#${user.userId}`,
            SK: 'PROFILE'
          },
          UpdateExpression: 'REMOVE coupleId, partnerId, coupleFormedAt SET coupleDisconnectedAt = :disconnectedAt',
          ExpressionAttributeValues: {
            ':disconnectedAt': new Date().toISOString()
          }
        }
      },
      // Remove couple info from user 2
      {
        Update: {
          TableName: process.env.DYNAMODB_TABLE,
          Key: {
            PK: `USER#${partnerId}`,
            SK: 'PROFILE'
          },
          UpdateExpression: 'REMOVE coupleId, partnerId, coupleFormedAt SET coupleDisconnectedAt = :disconnectedAt',
          ExpressionAttributeValues: {
            ':disconnectedAt': new Date().toISOString()
          }
        }
      }
    ];

    await database.transactWrite(transactItems);

    // Create disconnection event
    const disconnectionEvent = {
      PK: `EVENT#${Date.now()}`,
      SK: 'COUPLE_DISCONNECTED',
      eventType: 'COUPLE_DISCONNECTED',
      coupleId,
      disconnectedBy: user.userId,
      partnerId,
      timestamp: new Date().toISOString(),
      GSI1PK: 'TYPE#EVENT',
      GSI1SK: `COUPLE_DISCONNECTED#${coupleId}`,
      TTL: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
    };

    await database.put(disconnectionEvent);

    return response.success({
      message: 'Couple disconnected successfully',
      disconnectedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Disconnect couple error:', error);
    return response.handleError(error);
  }
}

/**
 * Get pending invitations for user
 */
async function handleGetInvitations(user) {
  try {
    // Get invitations sent to this user
    const receivedInvitations = await database.queryGSI1(
      'TYPE#INVITATION',
      {
        expression: 'begins_with(GSI1SK, :sk)',
        values: { ':sk': `TO#${user.userId}` }
      }
    );

    // Get invitations sent by this user
    const sentInvitations = await database.query(
      `INVITATION#${user.userId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'METADATA' }
      }
    );

    return response.success({
      received: receivedInvitations.items.filter(inv => inv.status === 'pending'),
      sent: sentInvitations.items.filter(inv => inv.status === 'pending'),
      total: {
        received: receivedInvitations.items.filter(inv => inv.status === 'pending').length,
        sent: sentInvitations.items.filter(inv => inv.status === 'pending').length
      }
    });
  } catch (error) {
    console.error('Get invitations error:', error);
    return response.handleError(error);
  }
}
