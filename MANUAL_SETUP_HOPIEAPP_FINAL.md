# 🚀 HopieApp - Configuración Manual Final

## 5. API Gateway REST

### 5.1 Crear API Gateway REST
1. Ve a **API Gateway** → **Create API**
2. **Choose an API type**: REST API → **Build**
3. **Create new API**: New API
4. **API name**: `hopie-api-dev`
5. **Description**: HopieApp REST API
6. **Endpoint Type**: Regional
7. Click **Create API**

### 5.2 Crear Authorizer de Cognito
1. Ve a **Authorizers** → **Create New Authorizer**
2. **Name**: `CognitoAuthorizer`
3. **Type**: Cognito
4. **Cognito User Pool**: Selecciona `hopie-user-pool-dev`
5. **Token Source**: `Authorization`
6. Click **Create**

### 5.3 Crear Recursos y Métodos

#### 5.3.1 Recurso /auth
1. **Actions** → **Create Resource**
2. **Resource Name**: `auth`
3. **Resource Path**: `/auth`
4. **Enable API Gateway CORS**: ✅
5. Click **Create Resource**

6. Selecciona `/auth` → **Actions** → **Create Resource**
7. **Resource Name**: `{proxy+}`
8. **Resource Path**: `/{proxy+}`
9. **Configure as proxy resource**: ✅
10. **Enable API Gateway CORS**: ✅
11. Click **Create Resource**

12. Selecciona `/{proxy+}` bajo `/auth` → **Actions** → **Create Method**
13. Selecciona **ANY** → Click ✓
14. **Integration type**: Lambda Function
15. **Use Lambda Proxy integration**: ✅
16. **Lambda Function**: `hopie-auth-dev`
17. **Authorization**: None
18. Click **Save** → **OK**

#### 5.3.2 Recurso /users
1. **Actions** → **Create Resource**
2. **Resource Name**: `users`
3. **Resource Path**: `/users`
4. **Enable API Gateway CORS**: ✅
5. Click **Create Resource**

6. Selecciona `/users` → **Actions** → **Create Resource**
7. **Resource Name**: `{proxy+}`
8. **Resource Path**: `/{proxy+}`
9. **Configure as proxy resource**: ✅
10. **Enable API Gateway CORS**: ✅
11. Click **Create Resource**

12. Selecciona `/{proxy+}` bajo `/users` → **Actions** → **Create Method**
13. Selecciona **ANY** → Click ✓
14. **Integration type**: Lambda Function
15. **Use Lambda Proxy integration**: ✅
16. **Lambda Function**: `hopie-user-dev`
17. **Authorization**: CognitoAuthorizer
18. Click **Save** → **OK**

#### 5.3.3 Repetir para todos los recursos restantes:

**Recursos a crear (todos con {proxy+} y método ANY):**

| Recurso | Lambda Function | Authorization |
|---------|----------------|---------------|
| `/couples` | `hopie-couple-dev` | CognitoAuthorizer |
| `/trees` | `hopie-tree-dev` | CognitoAuthorizer |
| `/questions` | `hopie-questions-dev` | CognitoAuthorizer |
| `/lifeplan` | `hopie-lifeplan-dev` | CognitoAuthorizer |
| `/places` | `hopie-places-dev` | CognitoAuthorizer |
| `/location` | `hopie-location-dev` | CognitoAuthorizer |
| `/stats` | `hopie-stats-dev` | CognitoAuthorizer |
| `/images` | `hopie-image-dev` | CognitoAuthorizer |
| `/notifications` | `hopie-notification-dev` | CognitoAuthorizer |

### 5.4 Desplegar API
1. **Actions** → **Deploy API**
2. **Deployment stage**: [New Stage]
3. **Stage name**: `dev`
4. **Stage description**: Development stage
5. Click **Deploy**

### 5.5 Configurar CORS (si es necesario)
1. Selecciona cada recurso `/{proxy+}`
2. **Actions** → **Enable CORS**
3. **Access-Control-Allow-Origin**: `*`
4. **Access-Control-Allow-Headers**: `Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token`
5. **Access-Control-Allow-Methods**: Seleccionar todos
6. Click **Enable CORS and replace existing CORS headers**

---

## 6. API Gateway WebSocket

### 6.1 Crear API Gateway WebSocket
1. Ve a **API Gateway** → **Create API**
2. **Choose an API type**: WebSocket API → **Build**
3. **API name**: `hopie-websocket-dev`
4. **Route selection expression**: `$request.body.action`
5. Click **Create API**

### 6.2 Crear Rutas
1. **Routes** → **Create Route**
2. **Route key**: `$connect`
3. **Integration**: Lambda Function
4. **Lambda Function**: `hopie-chat-dev`
5. Click **Create**

6. **Routes** → **Create Route**
7. **Route key**: `$disconnect`
8. **Integration**: Lambda Function
9. **Lambda Function**: `hopie-chat-dev`
10. Click **Create**

11. **Routes** → **Create Route**
12. **Route key**: `sendMessage`
13. **Integration**: Lambda Function
14. **Lambda Function**: `hopie-chat-dev`
15. Click **Create**

### 6.3 Desplegar WebSocket API
1. **Actions** → **Deploy API**
2. **Stage name**: `dev`
3. Click **Deploy**

---

## 7. CloudFront

### 7.1 Crear Distribución CloudFront
1. Ve a **CloudFront** → **Create Distribution**
2. **Origin Domain**: Selecciona tu bucket S3 `hopie-app-assets-dev-[RANDOM]`
3. **Origin Access**: Origin access control settings (recommended)
4. **Origin access control**: Create control setting
5. **Name**: `hopie-oac-dev`
6. Click **Create**

7. **Default cache behavior**:
   - **Viewer protocol policy**: Redirect HTTP to HTTPS
   - **Allowed HTTP methods**: GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE
   - **Cache policy**: CachingOptimized

8. **Settings**:
   - **Price class**: Use all edge locations
   - **Default root object**: `index.html`

9. Click **Create distribution**

### 7.2 Actualizar Bucket Policy
1. Ve al bucket S3 → **Permissions** → **Bucket policy**
2. Agregar la política que CloudFront te proporciona

---

## 8. EventBridge

### 8.1 Crear Regla para Pregunta Diaria
1. Ve a **EventBridge** → **Rules** → **Create rule**
2. **Name**: `hopie-daily-question-rule`
3. **Event bus**: default
4. **Rule type**: Schedule
5. **Schedule pattern**: Cron expression
6. **Cron expression**: `0 9 * * ? *` (9 AM todos los días)
7. **Target**: Lambda function
8. **Function**: `hopie-scheduler-dev`
9. Click **Create**

### 8.2 Crear Regla para Cálculo de Streaks
1. Ve a **EventBridge** → **Rules** → **Create rule**
2. **Name**: `hopie-streak-calculation-rule`
3. **Event bus**: default
4. **Rule type**: Schedule
5. **Schedule pattern**: Cron expression
6. **Cron expression**: `0 1 * * ? *` (1 AM todos los días)
7. **Target**: Lambda function
8. **Function**: `hopie-scheduler-dev`
9. Click **Create**

---

## 9. Configuración Final

### 9.1 URLs de tu aplicación

**API REST Base URL:**
```
https://[API-ID].execute-api.us-east-2.amazonaws.com/dev
```

**WebSocket URL:**
```
wss://[WEBSOCKET-ID].execute-api.us-east-2.amazonaws.com/dev
```

**CloudFront URL:**
```
https://[DISTRIBUTION-ID].cloudfront.net
```

### 9.2 Endpoints disponibles para Flutter

```bash
# Autenticación (sin autorización)
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/auth/login
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/auth/register
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/auth/refresh

# Usuarios (con autorización Cognito)
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/users/profile
PUT  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/users/profile
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/users/preferences
PUT  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/users/preferences

# Parejas
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/couples/create
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/couples/info
PUT  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/couples/update
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/couples/invite

# Árbol genealógico
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/trees/family
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/trees/add-member
PUT  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/trees/update-member
DELETE https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/trees/remove-member

# Preguntas diarias
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/questions/daily
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/questions/answer
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/questions/history
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/questions/categories

# Plan de vida
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/lifeplan/goals
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/lifeplan/create
PUT  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/lifeplan/update
DELETE https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/lifeplan/delete

# Lugares favoritos
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/places/favorites
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/places/add
PUT  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/places/update
DELETE https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/places/remove

# Geolocalización
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/location/update
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/location/current
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/location/history
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/location/share

# Estadísticas
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/stats/dashboard
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/stats/streaks
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/stats/activity
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/stats/update-streak

# Imágenes
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/images/upload
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/images/gallery
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/images/save-metadata
DELETE https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/images/delete

# Notificaciones
GET  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/notifications/list
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/notifications/mark-read
POST https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/notifications/send
PUT  https://[API-ID].execute-api.us-east-2.amazonaws.com/dev/notifications/preferences
```

### 9.3 Configuración en Flutter

```dart
// Configuración de URLs
class ApiConfig {
  static const String baseUrl = 'https://[API-ID].execute-api.us-east-2.amazonaws.com/dev';
  static const String websocketUrl = 'wss://[WEBSOCKET-ID].execute-api.us-east-2.amazonaws.com/dev';
  static const String cdnUrl = 'https://[DISTRIBUTION-ID].cloudfront.net';
  
  // Cognito
  static const String userPoolId = '[USER-POOL-ID]';
  static const String clientId = '[CLIENT-ID]';
  static const String region = 'us-east-2';
}
```

## 🎉 ¡Tu aplicación HopieApp está completamente configurada!

**Resumen de recursos creados:**
- ✅ 1 Tabla DynamoDB con 3 GSIs
- ✅ 1 Bucket S3 con CORS
- ✅ 1 Cognito User Pool con Google OAuth
- ✅ 14 Funciones Lambda
- ✅ 1 API Gateway REST con 11 endpoints
- ✅ 1 API Gateway WebSocket para chat
- ✅ 1 Distribución CloudFront
- ✅ 2 Reglas EventBridge para tareas programadas

**Total de recursos:** ~25 recursos de AWS configurados manualmente
